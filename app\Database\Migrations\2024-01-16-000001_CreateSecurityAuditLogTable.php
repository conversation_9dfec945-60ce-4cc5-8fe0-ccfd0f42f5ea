<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateSecurityAuditLogTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'event' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => false,
            ],
            'data' => [
                'type' => 'JSON',
                'null' => true,
            ],
            'ip_address' => [
                'type' => 'VARCHAR',
                'constraint' => 45,
                'null' => true,
            ],
            'user_agent' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'severity' => [
                'type' => 'ENUM',
                'constraint' => ['low', 'medium', 'high', 'critical'],
                'default' => 'medium',
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => false,
            ],
        ]);
        
        $this->forge->addKey('id', true);
        $this->forge->addKey('user_id');
        $this->forge->addKey('event');
        $this->forge->addKey('severity');
        $this->forge->addKey('created_at');
        $this->forge->createTable('security_audit_log');
    }

    public function down()
    {
        $this->forge->dropTable('security_audit_log');
    }
}
