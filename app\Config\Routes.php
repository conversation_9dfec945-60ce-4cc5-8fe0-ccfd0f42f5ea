<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
$routes->get('/', 'Home::index');

// Authentication Routes (Shield) - Load first
service('auth')->routes($routes);

// Custom Authentication Routes - Override specific routes
$routes->group('auth', function($routes) {
    // Google OAuth
    $routes->get('google', 'GoogleAuthController::redirect');
    $routes->get('google/callback', 'GoogleAuthController::callback');

    // Custom registration with role selection (override Shield's register)
    $routes->get('register', 'AuthController::registerView');
    $routes->post('register', 'AuthController::registerAction');

    // Activation check
    $routes->get('check-activation', 'AuthController::checkActivation');
});

// Convenience redirects for common auth URLs
$routes->get('login', function() {
    return redirect()->to('/auth/login');
});
$routes->get('register', function() {
    return redirect()->to('/auth/register');
});
$routes->get('logout', function() {
    return redirect()->to('/auth/logout');
});

// Admin Routes (Protected by authentication)
$routes->group('admin', ['namespace' => 'App\Controllers', 'filter' => 'session'], function($routes) {
    // Dashboard
    $routes->get('/', 'Admin::index');
    $routes->get('dashboard', 'Admin::dashboard');
    $routes->get('dashboard-updates', 'Admin::dashboardUpdates');
    $routes->post('mark-notification-read/(:num)', 'Admin::markNotificationRead/$1');
    $routes->post('mark-all-notifications-read', 'Admin::markAllNotificationsRead');

    // Workflow Management
    $routes->group('workflows', function($routes) {
        $routes->get('/', 'WorkflowController::index');
        $routes->post('start', 'WorkflowController::start');
        $routes->post('process-action/(:num)', 'WorkflowController::processAction/$1');
        $routes->get('status/(:num)', 'WorkflowController::status/$1');
        $routes->get('history/(:num)', 'WorkflowController::history/$1');
        $routes->post('bulk-action', 'WorkflowController::bulkAction');
    });

    // Students Management
    $routes->group('students', function($routes) {
        $routes->get('/', 'StudentsController::index');
        $routes->get('create', 'StudentsController::create');
        $routes->post('store', 'StudentsController::store');
        $routes->get('edit/(:num)', 'StudentsController::edit/$1');
        $routes->post('update/(:num)', 'StudentsController::update/$1');
        $routes->delete('delete/(:num)', 'StudentsController::delete/$1');
        $routes->get('show/(:num)', 'StudentsController::show/$1');
        $routes->post('toggle-status/(:num)', 'StudentsController::toggleStatus/$1');
        $routes->post('bulk-delete', 'StudentsController::bulkDelete');
        $routes->get('export/(:alpha)', 'StudentsController::export/$1');
        $routes->post('get-data', 'StudentsController::getData');
        $routes->post('get-by-class-section', 'StudentsController::getByClassSection');
        $routes->post('search', 'StudentsController::search');
        $routes->get('statistics', 'StudentsController::statistics');
        $routes->get('promote', 'StudentsController::promote');
        $routes->post('process-promotion', 'StudentsController::processPromotion');
        $routes->get('import', 'StudentsController::import');
        $routes->post('process-import', 'StudentsController::processImport');
        $routes->get('sessions/(:num)', 'StudentsController::sessions/$1');
        $routes->post('enroll-session', 'StudentsController::enrollSession');
        $routes->get('attendance/(:num)', 'StudentsController::attendance/$1');
        $routes->get('attendance', 'StudentsController::attendance');
        $routes->post('mark-attendance', 'StudentsController::markAttendance');
        $routes->post('attendance-report', 'StudentsController::attendanceReport');
        $routes->get('fees/(:num)', 'StudentsController::fees/$1');
        $routes->get('fees', 'StudentsController::fees');
        $routes->post('fee-collection-summary', 'StudentsController::feeCollectionSummary');
        $routes->post('defaulters', 'StudentsController::defaulters');
    });

    // Staff Management
    $routes->group('staff', function($routes) {
        $routes->get('/', 'StaffController::index');
        $routes->get('create', 'StaffController::create');
        $routes->post('store', 'StaffController::store');
        $routes->get('edit/(:num)', 'StaffController::edit/$1');
        $routes->post('update/(:num)', 'StaffController::update/$1');
        $routes->delete('delete/(:num)', 'StaffController::delete/$1');
        $routes->get('show/(:num)', 'StaffController::show/$1');
        $routes->post('toggle-status/(:num)', 'StaffController::toggleStatus/$1');
        $routes->post('bulk-delete', 'StaffController::bulkDelete');
        $routes->get('export/(:alpha)', 'StaffController::export/$1');
        $routes->post('get-data', 'StaffController::getData');
        $routes->post('get-by-department', 'StaffController::getByDepartment');
        $routes->get('statistics', 'StaffController::statistics');
        $routes->get('attendance', 'StaffController::attendance');
        $routes->post('mark-attendance', 'StaffController::markAttendance');
        $routes->get('attendance-report', 'StaffController::attendanceReport');
        $routes->get('import', 'StaffController::import');
        $routes->post('process-import', 'StaffController::processImport');
    });

    // Classes Management
    $routes->group('classes', function($routes) {
        $routes->get('/', 'ClassesController::index');
        $routes->get('create', 'ClassesController::create');
        $routes->post('store', 'ClassesController::store');
        $routes->get('edit/(:num)', 'ClassesController::edit/$1');
        $routes->post('update/(:num)', 'ClassesController::update/$1');
        $routes->delete('delete/(:num)', 'ClassesController::delete/$1');
        $routes->get('show/(:num)', 'ClassesController::show/$1');
        $routes->post('toggle-status/(:num)', 'ClassesController::toggleStatus/$1');
        $routes->post('bulk-delete', 'ClassesController::bulkDelete');
        $routes->get('export/(:alpha)', 'ClassesController::export/$1');
        $routes->post('get-data', 'ClassesController::getData');
        $routes->get('get-sections/(:num)', 'ClassesController::getSections/$1');
        $routes->post('assign-sections/(:num)', 'ClassesController::assignSections/$1');
        $routes->get('statistics', 'ClassesController::statistics');
        $routes->get('sections/(:num)', 'ClassesController::sections/$1');
    });

    // Expenses Management
    $routes->group('expenses', function($routes) {
        $routes->get('/', 'ExpensesController::index');
        $routes->get('create', 'ExpensesController::create');
        $routes->post('store', 'ExpensesController::store');
        $routes->get('edit/(:num)', 'ExpensesController::edit/$1');
        $routes->post('update/(:num)', 'ExpensesController::update/$1');
        $routes->delete('delete/(:num)', 'ExpensesController::delete/$1');
        $routes->get('show/(:num)', 'ExpensesController::show/$1');
        $routes->post('toggle-status/(:num)', 'ExpensesController::toggleStatus/$1');
        $routes->post('bulk-delete', 'ExpensesController::bulkDelete');
        $routes->get('export/(:alpha)', 'ExpensesController::export/$1');
        $routes->post('get-data', 'ExpensesController::getData');
        $routes->post('get-filtered', 'ExpensesController::getFiltered');
        $routes->get('statistics', 'ExpensesController::statistics');
        $routes->post('monthly-chart', 'ExpensesController::monthlyChart');
        $routes->post('category-chart', 'ExpensesController::categoryChart');
        $routes->post('soft-delete/(:num)', 'ExpensesController::softDelete/$1');
        $routes->post('restore/(:num)', 'ExpensesController::restore/$1');
        $routes->get('trash', 'ExpensesController::trash');
        $routes->get('reports', 'ExpensesController::reports');
        $routes->post('generate-report', 'ExpensesController::generateReport');
    });

    // Student Apps Management
    $routes->group('student-apps', function($routes) {
        // Main Student Apps Dashboard
        $routes->get('/', 'StudentAppsController::index');
        $routes->get('get-stats', 'StudentAppsController::getStats');

        // Student Apply Leave
        $routes->group('apply-leave', function($routes) {
            $routes->get('/', 'StudentApplyLeaveController::index');
            $routes->get('create', 'StudentApplyLeaveController::create');
            $routes->post('store', 'StudentApplyLeaveController::store');
            $routes->get('edit/(:num)', 'StudentApplyLeaveController::edit/$1');
            $routes->post('update/(:num)', 'StudentApplyLeaveController::update/$1');
            $routes->delete('delete/(:num)', 'StudentApplyLeaveController::delete/$1');
            $routes->get('show/(:num)', 'StudentApplyLeaveController::show/$1');
            $routes->post('get-data', 'StudentApplyLeaveController::getData');
            $routes->post('approve/(:num)', 'StudentApplyLeaveController::approve/$1');
            $routes->post('reject/(:num)', 'StudentApplyLeaveController::reject/$1');
            $routes->get('get-stats', 'StudentApplyLeaveController::getStats');
        });

        // Student Attendance
        $routes->group('attendance', function($routes) {
            $routes->get('/', 'StudentAttendanceController::index');
            $routes->get('create', 'StudentAttendanceController::create');
            $routes->post('store', 'StudentAttendanceController::store');
            $routes->get('edit/(:num)', 'StudentAttendanceController::edit/$1');
            $routes->post('update/(:num)', 'StudentAttendanceController::update/$1');
            $routes->delete('delete/(:num)', 'StudentAttendanceController::delete/$1');
            $routes->get('show/(:num)', 'StudentAttendanceController::show/$1');
            $routes->post('get-data', 'StudentAttendanceController::getData');
            $routes->get('get-stats', 'StudentAttendanceController::getStats');
        });

        // Student Behaviour
        $routes->group('behaviour', function($routes) {
            $routes->get('/', 'StudentBehaviourController::index');
            $routes->get('create', 'StudentBehaviourController::create');
            $routes->post('store', 'StudentBehaviourController::store');
            $routes->get('edit/(:num)', 'StudentBehaviourController::edit/$1');
            $routes->post('update/(:num)', 'StudentBehaviourController::update/$1');
            $routes->delete('delete/(:num)', 'StudentBehaviourController::delete/$1');
            $routes->get('show/(:num)', 'StudentBehaviourController::show/$1');
            $routes->post('get-data', 'StudentBehaviourController::getData');
            $routes->get('get-stats', 'StudentBehaviourController::getStats');
        });

        // Student Documents
        $routes->group('documents', function($routes) {
            $routes->get('/', 'StudentDocController::index');
            $routes->get('create', 'StudentDocController::create');
            $routes->post('store', 'StudentDocController::store');
            $routes->get('edit/(:num)', 'StudentDocController::edit/$1');
            $routes->post('update/(:num)', 'StudentDocController::update/$1');
            $routes->delete('delete/(:num)', 'StudentDocController::delete/$1');
            $routes->get('show/(:num)', 'StudentDocController::show/$1');
            $routes->post('get-data', 'StudentDocController::getData');
            $routes->get('download/(:num)', 'StudentDocController::download/$1');
            $routes->get('get-stats', 'StudentDocController::getStats');
        });

        // Student Fees
        $routes->group('fees', function($routes) {
            $routes->get('/', 'StudentFeesController::index');
            $routes->get('create', 'StudentFeesController::create');
            $routes->post('store', 'StudentFeesController::store');
            $routes->get('edit/(:num)', 'StudentFeesController::edit/$1');
            $routes->post('update/(:num)', 'StudentFeesController::update/$1');
            $routes->delete('delete/(:num)', 'StudentFeesController::delete/$1');
            $routes->get('show/(:num)', 'StudentFeesController::show/$1');
            $routes->post('get-data', 'StudentFeesController::getData');
            $routes->get('get-stats', 'StudentFeesController::getStats');
        });

        // Student Incidents
        $routes->group('incidents', function($routes) {
            $routes->get('/', 'StudentIncidentController::index');
            $routes->get('create', 'StudentIncidentController::create');
            $routes->post('store', 'StudentIncidentController::store');
            $routes->get('edit/(:num)', 'StudentIncidentController::edit/$1');
            $routes->post('update/(:num)', 'StudentIncidentController::update/$1');
            $routes->delete('delete/(:num)', 'StudentIncidentController::delete/$1');
            $routes->get('show/(:num)', 'StudentIncidentController::show/$1');
            $routes->post('get-data', 'StudentIncidentController::getData');
            $routes->get('get-stats', 'StudentIncidentController::getStats');
        });

        // Student Sessions
        $routes->group('sessions', function($routes) {
            $routes->get('/', 'StudentSessionController::index');
            $routes->get('create', 'StudentSessionController::create');
            $routes->post('store', 'StudentSessionController::store');
            $routes->get('edit/(:num)', 'StudentSessionController::edit/$1');
            $routes->post('update/(:num)', 'StudentSessionController::update/$1');
            $routes->delete('delete/(:num)', 'StudentSessionController::delete/$1');
            $routes->get('show/(:num)', 'StudentSessionController::show/$1');
            $routes->post('get-data', 'StudentSessionController::getData');
            $routes->get('get-stats', 'StudentSessionController::getStats');
        });

        // Student Subject Attendance
        $routes->group('subject-attendance', function($routes) {
            $routes->get('/', 'StudentSubjectAttendanceController::index');
            $routes->get('create', 'StudentSubjectAttendanceController::create');
            $routes->post('store', 'StudentSubjectAttendanceController::store');
            $routes->get('edit/(:num)', 'StudentSubjectAttendanceController::edit/$1');
            $routes->post('update/(:num)', 'StudentSubjectAttendanceController::update/$1');
            $routes->delete('delete/(:num)', 'StudentSubjectAttendanceController::delete/$1');
            $routes->get('show/(:num)', 'StudentSubjectAttendanceController::show/$1');
            $routes->post('get-data', 'StudentSubjectAttendanceController::getData');
            $routes->get('get-stats', 'StudentSubjectAttendanceController::getStats');
        });

        // Student Timeline
        $routes->group('timeline', function($routes) {
            $routes->get('/', 'StudentTimelineController::index');
            $routes->get('create', 'StudentTimelineController::create');
            $routes->post('store', 'StudentTimelineController::store');
            $routes->get('edit/(:num)', 'StudentTimelineController::edit/$1');
            $routes->post('update/(:num)', 'StudentTimelineController::update/$1');
            $routes->delete('delete/(:num)', 'StudentTimelineController::delete/$1');
            $routes->get('show/(:num)', 'StudentTimelineController::show/$1');
            $routes->post('get-data', 'StudentTimelineController::getData');
            $routes->get('get-stats', 'StudentTimelineController::getStats');
        });

        // Student Transport Fees
        $routes->group('transport-fees', function($routes) {
            $routes->get('/', 'StudentTransportFeesController::index');
            $routes->get('create', 'StudentTransportFeesController::create');
            $routes->post('store', 'StudentTransportFeesController::store');
            $routes->get('edit/(:num)', 'StudentTransportFeesController::edit/$1');
            $routes->post('update/(:num)', 'StudentTransportFeesController::update/$1');
            $routes->delete('delete/(:num)', 'StudentTransportFeesController::delete/$1');
            $routes->get('show/(:num)', 'StudentTransportFeesController::show/$1');
            $routes->post('get-data', 'StudentTransportFeesController::getData');
            $routes->get('get-stats', 'StudentTransportFeesController::getStats');
            $routes->get('get-stats', 'StudentApplyLeaveController::getStats');
        });

        // Student Attendance
        $routes->group('attendance', function($routes) {
            $routes->get('/', 'StudentAttendanceController::index');
            $routes->get('create', 'StudentAttendanceController::create');
            $routes->post('store', 'StudentAttendanceController::store');
            $routes->get('edit/(:num)', 'StudentAttendanceController::edit/$1');
            $routes->post('update/(:num)', 'StudentAttendanceController::update/$1');
            $routes->delete('delete/(:num)', 'StudentAttendanceController::delete/$1');
            $routes->get('show/(:num)', 'StudentAttendanceController::show/$1');
            $routes->post('get-data', 'StudentAttendanceController::getData');
            $routes->post('mark-attendance', 'StudentAttendanceController::markAttendance');
            $routes->get('get-stats', 'StudentAttendanceController::getStats');
        });

        // Student Behaviour
        $routes->group('behaviour', function($routes) {
            $routes->get('/', 'StudentBehaviourController::index');
            $routes->get('create', 'StudentBehaviourController::create');
            $routes->post('store', 'StudentBehaviourController::store');
            $routes->get('edit/(:num)', 'StudentBehaviourController::edit/$1');
            $routes->post('update/(:num)', 'StudentBehaviourController::update/$1');
            $routes->delete('delete/(:num)', 'StudentBehaviourController::delete/$1');
            $routes->get('show/(:num)', 'StudentBehaviourController::show/$1');
            $routes->post('get-data', 'StudentBehaviourController::getData');
        });

        // Student Documents
        $routes->group('documents', function($routes) {
            $routes->get('/', 'StudentDocController::index');
            $routes->get('create', 'StudentDocController::create');
            $routes->post('store', 'StudentDocController::store');
            $routes->get('edit/(:num)', 'StudentDocController::edit/$1');
            $routes->post('update/(:num)', 'StudentDocController::update/$1');
            $routes->delete('delete/(:num)', 'StudentDocController::delete/$1');
            $routes->get('show/(:num)', 'StudentDocController::show/$1');
            $routes->post('get-data', 'StudentDocController::getData');
            $routes->get('download/(:num)', 'StudentDocController::download/$1');
        });

        // Student Fees
        $routes->group('fees', function($routes) {
            $routes->get('/', 'StudentFeesController::index');
            $routes->get('create', 'StudentFeesController::create');
            $routes->post('store', 'StudentFeesController::store');
            $routes->get('edit/(:num)', 'StudentFeesController::edit/$1');
            $routes->post('update/(:num)', 'StudentFeesController::update/$1');
            $routes->delete('delete/(:num)', 'StudentFeesController::delete/$1');
            $routes->get('show/(:num)', 'StudentFeesController::show/$1');
            $routes->post('get-data', 'StudentFeesController::getData');
        });

        // Student Incidents
        $routes->group('incidents', function($routes) {
            $routes->get('/', 'StudentIncidentsController::index');
            $routes->get('create', 'StudentIncidentsController::create');
            $routes->post('store', 'StudentIncidentsController::store');
            $routes->get('edit/(:num)', 'StudentIncidentsController::edit/$1');
            $routes->post('update/(:num)', 'StudentIncidentsController::update/$1');
            $routes->delete('delete/(:num)', 'StudentIncidentsController::delete/$1');
            $routes->get('show/(:num)', 'StudentIncidentsController::show/$1');
            $routes->post('get-data', 'StudentIncidentsController::getData');
        });

        // Student Sessions
        $routes->group('sessions', function($routes) {
            $routes->get('/', 'StudentSessionController::index');
            $routes->get('create', 'StudentSessionController::create');
            $routes->post('store', 'StudentSessionController::store');
            $routes->get('edit/(:num)', 'StudentSessionController::edit/$1');
            $routes->post('update/(:num)', 'StudentSessionController::update/$1');
            $routes->delete('delete/(:num)', 'StudentSessionController::delete/$1');
            $routes->get('show/(:num)', 'StudentSessionController::show/$1');
            $routes->post('get-data', 'StudentSessionController::getData');
        });

        // Student Timeline
        $routes->group('timeline', function($routes) {
            $routes->get('/', 'StudentTimelineController::index');
            $routes->get('create', 'StudentTimelineController::create');
            $routes->post('store', 'StudentTimelineController::store');
            $routes->get('edit/(:num)', 'StudentTimelineController::edit/$1');
            $routes->post('update/(:num)', 'StudentTimelineController::update/$1');
            $routes->delete('delete/(:num)', 'StudentTimelineController::delete/$1');
            $routes->get('show/(:num)', 'StudentTimelineController::show/$1');
            $routes->post('get-data', 'StudentTimelineController::getData');
        });
    });

    // Legacy routes for backward compatibility
    $routes->get('fees', 'Admin::fees');
    $routes->get('reports', 'Admin::reports');
    $routes->get('settings', 'Admin::settings');
});

// Test routes
$routes->get('test', 'TestController::index');
$routes->get('test/sample', 'TestController::createSampleData');
