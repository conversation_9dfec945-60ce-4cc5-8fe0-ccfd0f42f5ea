// Service Worker for Student Management System PWA
// Provides offline functionality, caching, and push notifications

const CACHE_NAME = 'student-management-v1.0.0';
const STATIC_CACHE = 'static-v1.0.0';
const DYNAMIC_CACHE = 'dynamic-v1.0.0';

// Files to cache for offline functionality
const STATIC_FILES = [
    '/',
    '/admin',
    '/admin/dashboard',
    '/assets/admin/css/admin.css',
    '/assets/admin/js/admin.js',
    '/assets/vendor/tailwindcss/tailwind.min.css',
    '/assets/vendor/fontawesome/css/all.min.css',
    '/assets/vendor/jquery/jquery.min.js',
    '/assets/vendor/datatables/datatables.min.js',
    '/assets/vendor/sweetalert2/sweetalert2.min.js',
    '/assets/vendor/toastify/toastify.min.js',
    '/manifest.json',
    '/offline.html'
];

// API endpoints to cache
const API_CACHE_PATTERNS = [
    /\/admin\/api\/dashboard/,
    /\/admin\/api\/notifications/,
    /\/admin\/api\/stats/
];

// Install event - cache static files
self.addEventListener('install', event => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then(cache => {
                console.log('Service Worker: Caching static files');
                return cache.addAll(STATIC_FILES);
            })
            .then(() => {
                console.log('Service Worker: Static files cached');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('Service Worker: Error caching static files', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                            console.log('Service Worker: Deleting old cache', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker: Activated');
                return self.clients.claim();
            })
    );
});

// Fetch event - serve cached content when offline
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }
    
    // Skip external requests
    if (url.origin !== location.origin) {
        return;
    }
    
    event.respondWith(
        caches.match(request)
            .then(cachedResponse => {
                if (cachedResponse) {
                    // Return cached version and update cache in background
                    updateCache(request);
                    return cachedResponse;
                }
                
                // Not in cache, fetch from network
                return fetch(request)
                    .then(response => {
                        // Check if response is valid
                        if (!response || response.status !== 200 || response.type !== 'basic') {
                            return response;
                        }
                        
                        // Cache API responses and important pages
                        if (shouldCache(request)) {
                            const responseToCache = response.clone();
                            caches.open(DYNAMIC_CACHE)
                                .then(cache => {
                                    cache.put(request, responseToCache);
                                });
                        }
                        
                        return response;
                    })
                    .catch(error => {
                        console.log('Service Worker: Fetch failed, serving offline page', error);
                        
                        // Return offline page for navigation requests
                        if (request.destination === 'document') {
                            return caches.match('/offline.html');
                        }
                        
                        // Return cached version if available
                        return caches.match(request);
                    });
            })
    );
});

// Push notification event
self.addEventListener('push', event => {
    console.log('Service Worker: Push notification received');
    
    let notificationData = {
        title: 'Student Management System',
        body: 'You have a new notification',
        icon: '/assets/images/icon-192x192.png',
        badge: '/assets/images/badge-72x72.png',
        tag: 'default',
        data: {}
    };
    
    if (event.data) {
        try {
            const data = event.data.json();
            notificationData = { ...notificationData, ...data };
        } catch (error) {
            console.error('Service Worker: Error parsing push data', error);
        }
    }
    
    event.waitUntil(
        self.registration.showNotification(notificationData.title, {
            body: notificationData.body,
            icon: notificationData.icon,
            badge: notificationData.badge,
            tag: notificationData.tag,
            data: notificationData.data,
            actions: [
                {
                    action: 'view',
                    title: 'View',
                    icon: '/assets/images/view-icon.png'
                },
                {
                    action: 'dismiss',
                    title: 'Dismiss',
                    icon: '/assets/images/dismiss-icon.png'
                }
            ],
            requireInteraction: true,
            vibrate: [200, 100, 200]
        })
    );
});

// Notification click event
self.addEventListener('notificationclick', event => {
    console.log('Service Worker: Notification clicked');
    
    event.notification.close();
    
    if (event.action === 'view') {
        // Open the app or navigate to specific page
        const urlToOpen = event.notification.data.url || '/admin/dashboard';
        
        event.waitUntil(
            clients.matchAll({ type: 'window', includeUncontrolled: true })
                .then(clientList => {
                    // Check if app is already open
                    for (const client of clientList) {
                        if (client.url.includes('/admin') && 'focus' in client) {
                            client.focus();
                            client.navigate(urlToOpen);
                            return;
                        }
                    }
                    
                    // Open new window
                    if (clients.openWindow) {
                        return clients.openWindow(urlToOpen);
                    }
                })
        );
    }
});

// Background sync event
self.addEventListener('sync', event => {
    console.log('Service Worker: Background sync triggered');
    
    if (event.tag === 'background-sync') {
        event.waitUntil(doBackgroundSync());
    }
});

// Helper functions
function shouldCache(request) {
    const url = new URL(request.url);
    
    // Cache API responses
    for (const pattern of API_CACHE_PATTERNS) {
        if (pattern.test(url.pathname)) {
            return true;
        }
    }
    
    // Cache important pages
    const importantPages = ['/admin', '/admin/dashboard', '/admin/students', '/admin/notifications'];
    return importantPages.includes(url.pathname);
}

function updateCache(request) {
    fetch(request)
        .then(response => {
            if (response && response.status === 200) {
                caches.open(DYNAMIC_CACHE)
                    .then(cache => {
                        cache.put(request, response);
                    });
            }
        })
        .catch(error => {
            console.log('Service Worker: Cache update failed', error);
        });
}

function doBackgroundSync() {
    // Sync offline data when connection is restored
    return fetch('/admin/api/sync')
        .then(response => {
            if (response.ok) {
                console.log('Service Worker: Background sync completed');
                return response.json();
            }
            throw new Error('Sync failed');
        })
        .catch(error => {
            console.error('Service Worker: Background sync failed', error);
        });
}

// Message event for communication with main thread
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
    
    if (event.data && event.data.type === 'CACHE_UPDATE') {
        // Force cache update
        caches.delete(DYNAMIC_CACHE)
            .then(() => {
                console.log('Service Worker: Dynamic cache cleared');
            });
    }
});
