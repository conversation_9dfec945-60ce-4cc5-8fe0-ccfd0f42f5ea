<?php

namespace App\Controllers;

use App\Libraries\NotificationService;
use CodeIgniter\RESTful\ResourceController;

/**
 * Notification Controller
 * Handles real-time notifications and AJAX endpoints
 */
class NotificationController extends ResourceController
{
    protected $notificationService;
    protected $format = 'json';
    
    public function __construct()
    {
        $this->notificationService = new NotificationService();
    }

    /**
     * Get user notifications
     */
    public function index()
    {
        $limit = $this->request->getGet('limit') ?? 20;
        $offset = $this->request->getGet('offset') ?? 0;
        $type = $this->request->getGet('type');
        
        $notifications = $this->notificationService->getUserNotifications(
            auth()->id(), 
            $limit, 
            $offset, 
            $type
        );
        
        return $this->respond([
            'success' => true,
            'data' => $notifications,
            'pagination' => [
                'limit' => $limit,
                'offset' => $offset,
                'total' => $this->notificationService->getTotalNotifications(auth()->id())
            ]
        ]);
    }

    /**
     * Get unread notifications count
     */
    public function unreadCount()
    {
        $count = $this->notificationService->getUnreadCount(auth()->id());
        
        return $this->respond([
            'success' => true,
            'count' => $count
        ]);
    }

    /**
     * Mark notification as read
     */
    public function markRead($id = null)
    {
        if (!$id) {
            return $this->failValidationError('Notification ID is required');
        }

        $result = $this->notificationService->markAsRead($id, auth()->id());
        
        if ($result) {
            return $this->respond([
                'success' => true,
                'message' => 'Notification marked as read'
            ]);
        }
        
        return $this->failServerError('Failed to mark notification as read');
    }

    /**
     * Mark all notifications as read
     */
    public function markAllRead()
    {
        $result = $this->notificationService->markAllAsRead(auth()->id());
        
        if ($result) {
            return $this->respond([
                'success' => true,
                'message' => 'All notifications marked as read'
            ]);
        }
        
        return $this->failServerError('Failed to mark notifications as read');
    }

    /**
     * Delete notification
     */
    public function delete($id = null)
    {
        if (!$id) {
            return $this->failValidationError('Notification ID is required');
        }

        $result = $this->notificationService->deleteNotification($id, auth()->id());
        
        if ($result) {
            return $this->respond([
                'success' => true,
                'message' => 'Notification deleted'
            ]);
        }
        
        return $this->failServerError('Failed to delete notification');
    }

    /**
     * Get notification preferences
     */
    public function preferences()
    {
        $preferences = $this->notificationService->getUserPreferences(auth()->id());
        
        return $this->respond([
            'success' => true,
            'data' => $preferences
        ]);
    }

    /**
     * Update notification preferences
     */
    public function updatePreferences()
    {
        $preferences = $this->request->getJSON(true);
        
        if (!$preferences) {
            return $this->failValidationError('Invalid preferences data');
        }

        $result = $this->notificationService->updateUserPreferences(auth()->id(), $preferences);
        
        if ($result) {
            return $this->respond([
                'success' => true,
                'message' => 'Preferences updated successfully'
            ]);
        }
        
        return $this->failServerError('Failed to update preferences');
    }

    /**
     * Send test notification
     */
    public function sendTest()
    {
        if (!auth()->user()->can('admin.settings')) {
            return $this->failForbidden('Insufficient permissions');
        }

        $result = $this->notificationService->send(
            [auth()->id()],
            'Test Notification',
            'This is a test notification to verify the system is working correctly.',
            'info',
            ['test' => true]
        );
        
        if ($result) {
            return $this->respond([
                'success' => true,
                'message' => 'Test notification sent successfully'
            ]);
        }
        
        return $this->failServerError('Failed to send test notification');
    }

    /**
     * Real-time notification polling endpoint
     */
    public function poll()
    {
        $lastCheck = $this->request->getGet('last_check');
        $timestamp = $lastCheck ? strtotime($lastCheck) : time() - 60; // Default to last minute
        
        $notifications = $this->notificationService->getNotificationsSince(auth()->id(), $timestamp);
        
        return $this->respond([
            'success' => true,
            'data' => $notifications,
            'timestamp' => date('Y-m-d H:i:s'),
            'has_new' => !empty($notifications)
        ]);
    }

    /**
     * WebSocket connection endpoint
     */
    public function websocket()
    {
        // This would integrate with a WebSocket server like Ratchet or ReactPHP
        // For now, return connection info
        
        $token = $this->generateWebSocketToken();
        
        return $this->respond([
            'success' => true,
            'websocket_url' => base_url('ws'),
            'token' => $token,
            'user_id' => auth()->id()
        ]);
    }

    /**
     * Generate WebSocket authentication token
     */
    private function generateWebSocketToken(): string
    {
        $payload = [
            'user_id' => auth()->id(),
            'exp' => time() + 3600, // 1 hour expiry
            'iat' => time()
        ];
        
        // In a real implementation, you'd use JWT or similar
        return base64_encode(json_encode($payload));
    }

    /**
     * Broadcast notification to all users (admin only)
     */
    public function broadcast()
    {
        if (!auth()->user()->can('admin.settings')) {
            return $this->failForbidden('Insufficient permissions');
        }

        $data = $this->request->getJSON(true);
        
        if (!$data || !isset($data['title']) || !isset($data['message'])) {
            return $this->failValidationError('Title and message are required');
        }

        $roles = $data['roles'] ?? ['teacher', 'admin', 'staff'];
        $type = $data['type'] ?? 'info';
        
        $result = $this->notificationService->sendToMultipleRoles(
            $roles,
            $data['title'],
            $data['message'],
            $type,
            $data['data'] ?? []
        );
        
        if ($result) {
            return $this->respond([
                'success' => true,
                'message' => 'Broadcast notification sent successfully'
            ]);
        }
        
        return $this->failServerError('Failed to send broadcast notification');
    }

    /**
     * Get notification statistics for admin dashboard
     */
    public function statistics()
    {
        if (!auth()->user()->can('admin.reports')) {
            return $this->failForbidden('Insufficient permissions');
        }

        $stats = $this->notificationService->getSystemStatistics();
        
        return $this->respond([
            'success' => true,
            'data' => $stats
        ]);
    }
}
