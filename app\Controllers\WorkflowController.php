<?php

namespace App\Controllers;

use App\Libraries\WorkflowEngine;
use App\Libraries\NotificationService;

class WorkflowController extends BaseController
{
    protected $workflowEngine;
    protected $notificationService;

    public function __construct()
    {
        $this->workflowEngine = new WorkflowEngine();
        $this->notificationService = new NotificationService();
    }

    /**
     * Display workflow dashboard
     */
    public function index()
    {
        $data = [
            'title' => 'Workflow Management',
            'workflows' => $this->getWorkflowsForUser(),
            'workflow_stats' => $this->getWorkflowStats(),
            'pending_count' => $this->getPendingWorkflowsCount()
        ];

        return view('admin/workflows/index', $data);
    }

    /**
     * Start a new workflow
     */
    public function start()
    {
        if (!$this->request->isPost()) {
            return $this->response->setStatusCode(405);
        }

        $workflowType = $this->request->getPost('workflow_type');
        $data = $this->request->getPost('data');
        $initiatorId = auth()->id();

        // Validate input
        if (empty($workflowType) || empty($data)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Missing required fields'
            ]);
        }

        // Start workflow
        $result = $this->workflowEngine->startWorkflow($workflowType, $data, $initiatorId);

        if ($result['success']) {
            // Log workflow creation
            log_message('info', "Workflow started: {$workflowType} by user {$initiatorId}");
            
            // Send notification to relevant users
            $this->notifyWorkflowStakeholders($result['workflow_id'], 'started');
        }

        return $this->response->setJSON($result);
    }

    /**
     * Process workflow action
     */
    public function processAction($workflowId)
    {
        if (!$this->request->isPost()) {
            return $this->response->setStatusCode(405);
        }

        $action = $this->request->getPost('action');
        $actionData = $this->request->getPost('action_data', []);
        $userId = auth()->id();

        // Validate input
        if (empty($action)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Action is required'
            ]);
        }

        // Process the action
        $result = $this->workflowEngine->processAction($workflowId, $action, $userId, $actionData);

        if ($result['success']) {
            // Log action
            log_message('info', "Workflow action processed: {$action} on workflow {$workflowId} by user {$userId}");
            
            // Send notifications
            $this->notifyWorkflowStakeholders($workflowId, $action);
            
            // Show success message
            session()->setFlashdata('success', 'Action processed successfully');
        } else {
            // Show error message
            session()->setFlashdata('error', $result['message'] ?? 'Failed to process action');
        }

        return $this->response->setJSON($result);
    }

    /**
     * Get workflow status
     */
    public function status($workflowId)
    {
        $result = $this->workflowEngine->getWorkflowStatus($workflowId);
        return $this->response->setJSON($result);
    }

    /**
     * Get workflows for current user
     */
    private function getWorkflowsForUser(): array
    {
        $db = \Config\Database::connect();
        $builder = $db->table('workflows w');

        // Get workflows where user is involved
        $workflows = $builder
            ->select('w.*, wa.status as assignment_status, wa.step as current_assignment_step')
            ->join('workflow_assignments wa', 'wa.workflow_id = w.id', 'left')
            ->where('wa.user_id', auth()->id())
            ->orWhere('w.initiator_id', auth()->id())
            ->orderBy('w.created_at', 'DESC')
            ->limit(50)
            ->get()
            ->getResultArray();

        return $workflows;
    }

    /**
     * Get workflow statistics
     */
    private function getWorkflowStats(): array
    {
        $db = \Config\Database::connect();
        
        $stats = [
            'total' => $db->table('workflows')->countAllResults(),
            'active' => $db->table('workflows')->where('status', 'active')->countAllResults(),
            'completed' => $db->table('workflows')->where('status', 'completed')->countAllResults(),
            'pending_my_action' => $this->getPendingWorkflowsCount()
        ];

        // Get workflow types distribution
        $typeStats = $db->table('workflows')
            ->select('workflow_type, COUNT(*) as count')
            ->groupBy('workflow_type')
            ->get()
            ->getResultArray();

        $stats['by_type'] = array_column($typeStats, 'count', 'workflow_type');

        return $stats;
    }

    /**
     * Get count of workflows pending user's action
     */
    private function getPendingWorkflowsCount(): int
    {
        $db = \Config\Database::connect();
        
        return $db->table('workflow_assignments')
            ->where('user_id', auth()->id())
            ->where('status', 'pending')
            ->countAllResults();
    }

    /**
     * Notify workflow stakeholders
     */
    private function notifyWorkflowStakeholders(int $workflowId, string $action): void
    {
        try {
            $db = \Config\Database::connect();
            
            // Get workflow details
            $workflow = $db->table('workflows')->where('id', $workflowId)->get()->getRowArray();
            if (!$workflow) {
                return;
            }

            // Get all stakeholders (initiator + assigned users)
            $stakeholders = $db->table('workflow_assignments wa')
                ->select('wa.user_id, u.email, u.username')
                ->join('users u', 'u.id = wa.user_id')
                ->where('wa.workflow_id', $workflowId)
                ->get()
                ->getResultArray();

            // Add initiator if not already included
            $initiator = $db->table('users')->where('id', $workflow['initiator_id'])->get()->getRowArray();
            if ($initiator) {
                $stakeholders[] = [
                    'user_id' => $initiator['id'],
                    'email' => $initiator['email'],
                    'username' => $initiator['username']
                ];
            }

            // Send notifications
            foreach ($stakeholders as $stakeholder) {
                if ($stakeholder['user_id'] != auth()->id()) { // Don't notify the actor
                    $this->notificationService->create([
                        'user_id' => $stakeholder['user_id'],
                        'title' => "Workflow {$action}",
                        'message' => "Workflow #{$workflowId} ({$workflow['workflow_type']}) has been {$action}",
                        'type' => 'workflow',
                        'data' => json_encode([
                            'workflow_id' => $workflowId,
                            'action' => $action,
                            'workflow_type' => $workflow['workflow_type']
                        ])
                    ]);
                }
            }
        } catch (\Exception $e) {
            log_message('error', 'Failed to notify workflow stakeholders: ' . $e->getMessage());
        }
    }

    /**
     * Get workflow history
     */
    public function history($workflowId)
    {
        $db = \Config\Database::connect();
        
        $history = $db->table('workflow_actions wa')
            ->select('wa.*, u.username')
            ->join('users u', 'u.id = wa.user_id')
            ->where('wa.workflow_id', $workflowId)
            ->orderBy('wa.created_at', 'ASC')
            ->get()
            ->getResultArray();

        return $this->response->setJSON([
            'success' => true,
            'history' => $history
        ]);
    }

    /**
     * Bulk workflow actions
     */
    public function bulkAction()
    {
        if (!$this->request->isPost()) {
            return $this->response->setStatusCode(405);
        }

        $workflowIds = $this->request->getPost('workflow_ids');
        $action = $this->request->getPost('action');
        $userId = auth()->id();

        if (empty($workflowIds) || empty($action)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Missing required fields'
            ]);
        }

        $results = [];
        $successCount = 0;

        foreach ($workflowIds as $workflowId) {
            $result = $this->workflowEngine->processAction($workflowId, $action, $userId);
            $results[$workflowId] = $result;
            
            if ($result['success']) {
                $successCount++;
                $this->notifyWorkflowStakeholders($workflowId, $action);
            }
        }

        return $this->response->setJSON([
            'success' => $successCount > 0,
            'message' => "Processed {$successCount} out of " . count($workflowIds) . " workflows",
            'results' => $results
        ]);
    }
}
