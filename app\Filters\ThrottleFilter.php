<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

/**
 * Request Throttling Filter
 * Prevents abuse by limiting requests per IP/user
 */
class ThrottleFilter implements FilterInterface
{
    private $cache;
    
    public function __construct()
    {
        $this->cache = \Config\Services::cache();
    }

    /**
     * Do whatever processing this filter needs to do.
     *
     * @param RequestInterface $request
     * @param array|null       $arguments Expected format: [maxRequests, timeWindow] e.g., [60, 3600] = 60 requests per hour
     *
     * @return RequestInterface|ResponseInterface|string|void
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        // Default throttle settings
        $maxRequests = $arguments[0] ?? 100; // Max requests
        $timeWindow = $arguments[1] ?? 3600;  // Time window in seconds (1 hour)
        
        // Get identifier (IP + User ID if logged in)
        $identifier = $this->getIdentifier($request);
        
        // Check current request count
        $cacheKey = "throttle_{$identifier}";
        $currentCount = $this->cache->get($cacheKey) ?? 0;
        
        if ($currentCount >= $maxRequests) {
            // Log throttling event
            log_message('warning', sprintf(
                'Request throttled: %s exceeded %d requests in %d seconds',
                $identifier,
                $maxRequests,
                $timeWindow
            ));
            
            return $this->throttledResponse($request, $timeWindow);
        }
        
        // Increment counter
        $this->cache->save($cacheKey, $currentCount + 1, $timeWindow);
        
        // Add rate limit headers
        $request->throttleInfo = [
            'limit' => $maxRequests,
            'remaining' => $maxRequests - ($currentCount + 1),
            'reset' => time() + $timeWindow
        ];
    }

    /**
     * Allows After filters to inspect and modify the response
     * object as needed.
     *
     * @param RequestInterface  $request
     * @param ResponseInterface $response
     * @param array|null        $arguments
     *
     * @return ResponseInterface|void
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Add rate limit headers to response
        if (isset($request->throttleInfo)) {
            $info = $request->throttleInfo;
            $response->setHeader('X-RateLimit-Limit', $info['limit']);
            $response->setHeader('X-RateLimit-Remaining', $info['remaining']);
            $response->setHeader('X-RateLimit-Reset', $info['reset']);
        }
        
        return $response;
    }

    /**
     * Get unique identifier for throttling
     */
    private function getIdentifier(RequestInterface $request): string
    {
        $ip = $request->getIPAddress();
        
        // If user is logged in, include user ID for more specific throttling
        if (auth()->loggedIn()) {
            return $ip . '_user_' . auth()->id();
        }
        
        return $ip;
    }

    /**
     * Return throttled response
     */
    private function throttledResponse(RequestInterface $request, int $timeWindow): ResponseInterface
    {
        $retryAfter = $timeWindow;
        
        if ($request->isAJAX()) {
            return service('response')->setJSON([
                'success' => false,
                'message' => 'Too many requests. Please try again later.',
                'code' => 'RATE_LIMITED',
                'retry_after' => $retryAfter
            ])->setStatusCode(429)
              ->setHeader('Retry-After', $retryAfter);
        }

        // For regular requests, show error page
        return service('response')
            ->setStatusCode(429)
            ->setHeader('Retry-After', $retryAfter)
            ->setBody(view('errors/html/error_429', [
                'message' => 'Too many requests. Please try again later.',
                'retry_after' => $retryAfter
            ]));
    }
}
