<?php

namespace App\Libraries;

use App\Libraries\NotificationService;
use CodeIgniter\Events\Events;

/**
 * Workflow Engine
 * Manages business process workflows with approval chains and automation
 */
class WorkflowEngine
{
    private $notificationService;
    private $db;
    
    public function __construct()
    {
        $this->notificationService = new NotificationService();
        $this->db = \Config\Database::connect();
    }

    /**
     * Start a new workflow
     */
    public function startWorkflow(string $workflowType, array $data, int $initiatorId): array
    {
        $workflowConfig = $this->getWorkflowConfig($workflowType);
        
        if (!$workflowConfig) {
            return ['success' => false, 'message' => 'Invalid workflow type'];
        }

        // Create workflow instance
        $workflowId = $this->createWorkflowInstance($workflowType, $data, $initiatorId, $workflowConfig);
        
        if (!$workflowId) {
            return ['success' => false, 'message' => 'Failed to create workflow instance'];
        }

        // Execute first step
        $result = $this->executeWorkflowStep($workflowId, $workflowConfig['steps'][0]);
        
        // Send notifications
        $this->notificationService->sendWorkflowNotification($workflowType, $data, 'started');
        
        // Trigger workflow event
        Events::trigger('workflow_started', [
            'workflow_id' => $workflowId,
            'workflow_type' => $workflowType,
            'data' => $data,
            'initiator_id' => $initiatorId
        ]);

        return [
            'success' => true,
            'workflow_id' => $workflowId,
            'current_step' => $workflowConfig['steps'][0]['name'],
            'message' => 'Workflow started successfully'
        ];
    }

    /**
     * Process workflow action (approve, reject, etc.)
     */
    public function processAction(int $workflowId, string $action, int $userId, array $actionData = []): array
    {
        $workflow = $this->getWorkflowInstance($workflowId);
        
        if (!$workflow) {
            return ['success' => false, 'message' => 'Workflow not found'];
        }

        $workflowConfig = $this->getWorkflowConfig($workflow['workflow_type']);
        $currentStep = $this->getCurrentStep($workflow, $workflowConfig);
        
        if (!$currentStep) {
            return ['success' => false, 'message' => 'Invalid workflow state'];
        }

        // Validate user permissions for this action
        if (!$this->canUserPerformAction($userId, $currentStep, $action)) {
            return ['success' => false, 'message' => 'Insufficient permissions'];
        }

        // Record the action
        $this->recordWorkflowAction($workflowId, $userId, $action, $actionData);
        
        // Process the action
        $result = $this->executeAction($workflow, $currentStep, $action, $userId, $actionData);
        
        if ($result['success']) {
            // Move to next step or complete workflow
            $nextResult = $this->moveToNextStep($workflow, $workflowConfig, $action);
            
            // Send notifications
            $this->notificationService->sendWorkflowNotification(
                $workflow['workflow_type'], 
                json_decode($workflow['data'], true), 
                $action
            );
            
            // Trigger workflow event
            Events::trigger('workflow_action_processed', [
                'workflow_id' => $workflowId,
                'action' => $action,
                'user_id' => $userId,
                'result' => $result
            ]);
        }
        
        return $result;
    }

    /**
     * Get workflow status
     */
    public function getWorkflowStatus(int $workflowId): array
    {
        $workflow = $this->getWorkflowInstance($workflowId);
        
        if (!$workflow) {
            return ['success' => false, 'message' => 'Workflow not found'];
        }

        $workflowConfig = $this->getWorkflowConfig($workflow['workflow_type']);
        $currentStep = $this->getCurrentStep($workflow, $workflowConfig);
        $actions = $this->getWorkflowActions($workflowId);
        
        return [
            'success' => true,
            'workflow' => $workflow,
            'current_step' => $currentStep,
            'actions' => $actions,
            'progress' => $this->calculateProgress($workflow, $workflowConfig)
        ];
    }

    /**
     * Get workflow configuration
     */
    private function getWorkflowConfig(string $workflowType): ?array
    {
        $configs = [
            'student_leave' => [
                'name' => 'Student Leave Application',
                'steps' => [
                    [
                        'name' => 'submitted',
                        'title' => 'Application Submitted',
                        'actions' => ['approve', 'reject', 'request_info'],
                        'roles' => ['teacher', 'admin', 'principal'],
                        'auto_approve' => false
                    ],
                    [
                        'name' => 'approved',
                        'title' => 'Application Approved',
                        'actions' => ['complete'],
                        'roles' => ['system'],
                        'auto_approve' => true
                    ],
                    [
                        'name' => 'rejected',
                        'title' => 'Application Rejected',
                        'actions' => ['resubmit'],
                        'roles' => ['student', 'parent'],
                        'auto_approve' => false
                    ]
                ]
            ],
            'student_admission' => [
                'name' => 'Student Admission Process',
                'steps' => [
                    [
                        'name' => 'application_received',
                        'title' => 'Application Received',
                        'actions' => ['review', 'reject'],
                        'roles' => ['admin', 'principal'],
                        'auto_approve' => false
                    ],
                    [
                        'name' => 'under_review',
                        'title' => 'Under Review',
                        'actions' => ['approve', 'reject', 'request_documents'],
                        'roles' => ['admin', 'principal'],
                        'auto_approve' => false
                    ],
                    [
                        'name' => 'approved',
                        'title' => 'Admission Approved',
                        'actions' => ['complete'],
                        'roles' => ['system'],
                        'auto_approve' => true
                    ]
                ]
            ],
            'fee_payment' => [
                'name' => 'Fee Payment Verification',
                'steps' => [
                    [
                        'name' => 'payment_submitted',
                        'title' => 'Payment Submitted',
                        'actions' => ['verify', 'reject'],
                        'roles' => ['admin', 'staff'],
                        'auto_approve' => false
                    ],
                    [
                        'name' => 'verified',
                        'title' => 'Payment Verified',
                        'actions' => ['complete'],
                        'roles' => ['system'],
                        'auto_approve' => true
                    ]
                ]
            ]
        ];
        
        return $configs[$workflowType] ?? null;
    }

    /**
     * Create workflow instance in database
     */
    private function createWorkflowInstance(string $workflowType, array $data, int $initiatorId, array $config): ?int
    {
        $workflowData = [
            'workflow_type' => $workflowType,
            'data' => json_encode($data),
            'initiator_id' => $initiatorId,
            'current_step' => $config['steps'][0]['name'],
            'status' => 'active',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $builder = $this->db->table('workflows');
        
        if ($builder->insert($workflowData)) {
            return $this->db->insertID();
        }
        
        return null;
    }

    /**
     * Get workflow instance
     */
    private function getWorkflowInstance(int $workflowId): ?array
    {
        $builder = $this->db->table('workflows');
        $workflow = $builder->where('id', $workflowId)->get()->getRowArray();
        
        return $workflow ?: null;
    }

    /**
     * Get current step configuration
     */
    private function getCurrentStep(array $workflow, array $config): ?array
    {
        foreach ($config['steps'] as $step) {
            if ($step['name'] === $workflow['current_step']) {
                return $step;
            }
        }
        
        return null;
    }

    /**
     * Check if user can perform action
     */
    private function canUserPerformAction(int $userId, array $step, string $action): bool
    {
        // Check if action is allowed in current step
        if (!in_array($action, $step['actions'])) {
            return false;
        }
        
        // Check user roles
        $userModel = new \CodeIgniter\Shield\Models\UserModel();
        $user = $userModel->find($userId);
        
        if (!$user) {
            return false;
        }
        
        $userRoles = $user->getGroups();
        
        // Check if user has required role
        foreach ($step['roles'] as $requiredRole) {
            if (in_array($requiredRole, $userRoles) || $requiredRole === 'system') {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Record workflow action
     */
    private function recordWorkflowAction(int $workflowId, int $userId, string $action, array $data): bool
    {
        $actionData = [
            'workflow_id' => $workflowId,
            'user_id' => $userId,
            'action' => $action,
            'data' => json_encode($data),
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $builder = $this->db->table('workflow_actions');
        return $builder->insert($actionData);
    }

    /**
     * Execute workflow action
     */
    private function executeAction(array $workflow, array $step, string $action, int $userId, array $data): array
    {
        // This would contain business logic for each action type
        switch ($action) {
            case 'approve':
                return $this->executeApproveAction($workflow, $data);
                
            case 'reject':
                return $this->executeRejectAction($workflow, $data);
                
            case 'request_info':
                return $this->executeRequestInfoAction($workflow, $data);
                
            default:
                return ['success' => true, 'message' => 'Action executed'];
        }
    }

    /**
     * Move workflow to next step
     */
    private function moveToNextStep(array $workflow, array $config, string $action): array
    {
        $nextStep = $this->determineNextStep($workflow['current_step'], $action, $config);
        
        if ($nextStep) {
            $builder = $this->db->table('workflows');
            $updated = $builder->where('id', $workflow['id'])->update([
                'current_step' => $nextStep,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            
            return ['success' => $updated, 'next_step' => $nextStep];
        }
        
        return ['success' => false, 'message' => 'Could not determine next step'];
    }

    /**
     * Determine next step based on current step and action
     */
    private function determineNextStep(string $currentStep, string $action, array $config): ?string
    {
        // Simple workflow logic - this could be more complex
        switch ($action) {
            case 'approve':
                return 'approved';
            case 'reject':
                return 'rejected';
            case 'complete':
                return 'completed';
            default:
                return null;
        }
    }

    // Additional helper methods...
    private function getWorkflowActions(int $workflowId): array { return []; }
    private function calculateProgress(array $workflow, array $config): int { return 50; }
    private function executeApproveAction(array $workflow, array $data): array { return ['success' => true]; }
    private function executeRejectAction(array $workflow, array $data): array { return ['success' => true]; }
    private function executeRequestInfoAction(array $workflow, array $data): array { return ['success' => true]; }
}
