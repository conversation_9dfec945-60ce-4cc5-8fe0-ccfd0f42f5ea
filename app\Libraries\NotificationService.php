<?php

namespace App\Libraries;

use App\Models\NotificationModel;
use CodeIgniter\Events\Events;

/**
 * Enhanced Notification Service
 * Provides real-time notifications, email alerts, and system notifications
 */
class NotificationService
{
    private $notificationModel;
    private $emailService;
    
    public function __construct()
    {
        $this->notificationModel = new NotificationModel();
        $this->emailService = \Config\Services::email();
    }

    /**
     * Send notification to user(s)
     */
    public function send(array $userIds, string $title, string $message, string $type = 'info', array $data = []): bool
    {
        $success = true;
        
        foreach ($userIds as $userId) {
            // Create database notification
            $notificationCreated = $this->notificationModel->createNotification($userId, $title, $message, $type, $data);
            
            if (!$notificationCreated) {
                $success = false;
                continue;
            }
            
            // Trigger real-time notification event
            Events::trigger('notification_created', [
                'user_id' => $userId,
                'title' => $title,
                'message' => $message,
                'type' => $type,
                'data' => $data
            ]);
            
            // Send email notification if configured
            if ($this->shouldSendEmail($type, $data)) {
                $this->sendEmailNotification($userId, $title, $message, $type);
            }
        }
        
        return $success;
    }

    /**
     * Send notification to users with specific role
     */
    public function sendToRole(string $role, string $title, string $message, string $type = 'info', array $data = []): bool
    {
        $userModel = new \CodeIgniter\Shield\Models\UserModel();
        $users = $userModel->withGroup($role)->findAll();
        
        $userIds = array_column($users, 'id');
        
        return $this->send($userIds, $title, $message, $type, $data);
    }

    /**
     * Send notification to users with specific permission
     */
    public function sendToPermission(string $permission, string $title, string $message, string $type = 'info', array $data = []): bool
    {
        // Get users with specific permission
        $userModel = new \CodeIgniter\Shield\Models\UserModel();
        $users = $userModel->withPermission($permission)->findAll();
        
        $userIds = array_column($users, 'id');
        
        return $this->send($userIds, $title, $message, $type, $data);
    }

    /**
     * Create workflow notification
     */
    public function sendWorkflowNotification(string $workflowType, array $workflowData, string $action): bool
    {
        $notifications = $this->getWorkflowNotifications($workflowType, $workflowData, $action);
        
        $success = true;
        foreach ($notifications as $notification) {
            $sent = $this->send(
                $notification['user_ids'],
                $notification['title'],
                $notification['message'],
                $notification['type'],
                $notification['data']
            );
            
            if (!$sent) {
                $success = false;
            }
        }
        
        return $success;
    }

    /**
     * Get unread notifications for user
     */
    public function getUnreadNotifications(int $userId, int $limit = 10): array
    {
        return $this->notificationModel->getUnreadNotifications($userId, $limit);
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(int $notificationId, int $userId): bool
    {
        return $this->notificationModel->markAsRead($notificationId, $userId);
    }

    /**
     * Mark all notifications as read for user
     */
    public function markAllAsRead(int $userId): bool
    {
        return $this->notificationModel->markAllAsRead($userId);
    }

    /**
     * Get notification statistics
     */
    public function getNotificationStats(int $userId): array
    {
        return [
            'total' => $this->notificationModel->getTotalNotifications($userId),
            'unread' => $this->notificationModel->getUnreadCount($userId),
            'today' => $this->notificationModel->getTodayNotifications($userId),
            'this_week' => $this->notificationModel->getWeekNotifications($userId)
        ];
    }

    /**
     * Determine if email should be sent
     */
    private function shouldSendEmail(string $type, array $data): bool
    {
        // Check user preferences and notification type
        $emailTypes = ['error', 'warning', 'auth', 'workflow'];
        
        return in_array($type, $emailTypes) || 
               (isset($data['send_email']) && $data['send_email'] === true);
    }

    /**
     * Send email notification
     */
    private function sendEmailNotification(int $userId, string $title, string $message, string $type): bool
    {
        try {
            $userModel = new \CodeIgniter\Shield\Models\UserModel();
            $user = $userModel->find($userId);
            
            if (!$user || !$user->email) {
                return false;
            }

            $this->emailService->setTo($user->email);
            $this->emailService->setSubject($title);
            
            $emailData = [
                'title' => $title,
                'message' => $message,
                'type' => $type,
                'user' => $user,
                'app_name' => 'Student Management System'
            ];
            
            $this->emailService->setMessage(view('emails/notification', $emailData));
            
            return $this->emailService->send();
            
        } catch (\Exception $e) {
            log_message('error', 'Failed to send email notification: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get workflow-specific notifications
     */
    private function getWorkflowNotifications(string $workflowType, array $workflowData, string $action): array
    {
        $notifications = [];
        
        switch ($workflowType) {
            case 'student_leave':
                $notifications = $this->getLeaveWorkflowNotifications($workflowData, $action);
                break;
                
            case 'student_admission':
                $notifications = $this->getAdmissionWorkflowNotifications($workflowData, $action);
                break;
                
            case 'fee_payment':
                $notifications = $this->getFeeWorkflowNotifications($workflowData, $action);
                break;
                
            case 'incident_report':
                $notifications = $this->getIncidentWorkflowNotifications($workflowData, $action);
                break;
        }
        
        return $notifications;
    }

    /**
     * Get leave workflow notifications
     */
    private function getLeaveWorkflowNotifications(array $data, string $action): array
    {
        $notifications = [];
        
        switch ($action) {
            case 'submitted':
                // Notify teachers and admin
                $notifications[] = [
                    'user_ids' => $this->getUserIdsByRole(['teacher', 'admin', 'principal']),
                    'title' => 'New Leave Application',
                    'message' => "Student {$data['student_name']} has submitted a leave application.",
                    'type' => 'info',
                    'data' => ['leave_id' => $data['leave_id'], 'workflow' => 'student_leave']
                ];
                break;
                
            case 'approved':
                // Notify student and parents
                $notifications[] = [
                    'user_ids' => [$data['student_user_id'], $data['parent_user_id']],
                    'title' => 'Leave Application Approved',
                    'message' => "Leave application has been approved.",
                    'type' => 'success',
                    'data' => ['leave_id' => $data['leave_id'], 'workflow' => 'student_leave']
                ];
                break;
                
            case 'rejected':
                // Notify student and parents
                $notifications[] = [
                    'user_ids' => [$data['student_user_id'], $data['parent_user_id']],
                    'title' => 'Leave Application Rejected',
                    'message' => "Leave application has been rejected. Reason: {$data['reason']}",
                    'type' => 'warning',
                    'data' => ['leave_id' => $data['leave_id'], 'workflow' => 'student_leave']
                ];
                break;
        }
        
        return $notifications;
    }

    /**
     * Get user IDs by role
     */
    private function getUserIdsByRole(array $roles): array
    {
        $userModel = new \CodeIgniter\Shield\Models\UserModel();
        $userIds = [];
        
        foreach ($roles as $role) {
            $users = $userModel->withGroup($role)->findAll();
            $userIds = array_merge($userIds, array_column($users, 'id'));
        }
        
        return array_unique($userIds);
    }

    // Additional workflow notification methods would be implemented here...
    private function getAdmissionWorkflowNotifications(array $data, string $action): array { return []; }
    private function getFeeWorkflowNotifications(array $data, string $action): array { return []; }
    private function getIncidentWorkflowNotifications(array $data, string $action): array { return []; }
}
