<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

/**
 * Role-based Authorization Filter
 * Checks if user has required roles for specific actions
 */
class RoleFilter implements FilterInterface
{
    /**
     * Do whatever processing this filter needs to do.
     *
     * @param RequestInterface $request
     * @param array|null       $arguments Expected format: ['role1', 'role2'] or ['role1']
     *
     * @return RequestInterface|ResponseInterface|string|void
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        // Ensure user is authenticated first
        if (!auth()->loggedIn()) {
            return $this->unauthorizedResponse($request, 'Authentication required');
        }

        $user = auth()->user();
        
        // If no specific roles required, just check if authenticated
        if (empty($arguments)) {
            return;
        }

        // Check if user has any of the required roles
        $hasRole = false;
        foreach ($arguments as $role) {
            if ($user->inGroup($role)) {
                $hasRole = true;
                break;
            }
        }

        if (!$hasRole) {
            // Log unauthorized access attempt
            log_message('warning', sprintf(
                'Unauthorized role access attempt: User ID %d (roles: %s) tried to access %s %s requiring roles: %s',
                auth()->id(),
                implode(', ', $user->getGroups()),
                $request->getMethod(),
                $request->getUri()->getPath(),
                implode(', ', $arguments)
            ));

            return $this->unauthorizedResponse($request, 'Insufficient role privileges');
        }

        // Log successful role check for audit
        log_message('info', sprintf(
            'Role access granted: User ID %d (roles: %s) accessed %s %s',
            auth()->id(),
            implode(', ', $user->getGroups()),
            $request->getMethod(),
            $request->getUri()->getPath()
        ));
    }

    /**
     * Allows After filters to inspect and modify the response
     * object as needed.
     *
     * @param RequestInterface  $request
     * @param ResponseInterface $response
     * @param array|null        $arguments
     *
     * @return ResponseInterface|void
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Add role-specific headers or modifications if needed
        if (auth()->loggedIn()) {
            $user = auth()->user();
            $roles = implode(',', $user->getGroups());
            $response->setHeader('X-User-Roles', $roles);
        }
        
        return $response;
    }

    /**
     * Return appropriate unauthorized response
     */
    private function unauthorizedResponse(RequestInterface $request, string $message): ResponseInterface
    {
        if ($request->isAJAX()) {
            return service('response')->setJSON([
                'success' => false,
                'message' => $message,
                'code' => 'INSUFFICIENT_ROLE'
            ])->setStatusCode(403);
        }

        // For regular requests, redirect to access denied page or dashboard
        return redirect()->to('/admin')->with('error', 'Your role does not have access to this resource.');
    }
}
