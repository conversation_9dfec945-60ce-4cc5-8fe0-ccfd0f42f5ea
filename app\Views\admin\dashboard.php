<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<!-- Enhanced <PERSON><PERSON> with Welcome Message -->
<div class="mb-8">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div class="mb-4 lg:mb-0">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                Welcome back, <?= esc(auth()->user()->username ?? 'Admin') ?>! 👋
            </h1>
            <p class="text-gray-600 dark:text-gray-400">
                Here's what's happening with your school today.
            </p>
        </div>

        <!-- Quick Actions Dropdown -->
        <div class="flex items-center space-x-4">
            <div class="relative">
                <button id="quickActionsBtn" class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors duration-200">
                    <i class="fas fa-plus mr-2"></i>
                    Quick Actions
                    <i class="fas fa-chevron-down ml-2"></i>
                </button>
                <div id="quickActionsMenu" class="hidden absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg z-50">
                    <a href="<?= base_url('admin/students/create') ?>" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-user-plus mr-2"></i>Add Student
                    </a>
                    <a href="<?= base_url('admin/staff/create') ?>" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-user-tie mr-2"></i>Add Staff
                    </a>
                    <a href="<?= base_url('admin/classes/create') ?>" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-school mr-2"></i>Add Class
                    </a>
                </div>
            </div>

            <!-- Notifications Bell -->
            <div class="relative">
                <button id="notificationBtn" class="relative p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors duration-200">
                    <i class="fas fa-bell text-xl"></i>
                    <?php if (isset($notification_stats['unread']) && $notification_stats['unread'] > 0): ?>
                        <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                            <?= $notification_stats['unread'] ?>
                        </span>
                    <?php endif; ?>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Dashboard Stats with Animations -->
<div class="grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-4 mb-8">
    <!-- Total Students Card -->
    <div class="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-500 to-blue-600 p-6 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="absolute top-0 right-0 -mt-4 -mr-4 h-16 w-16 rounded-full bg-white bg-opacity-20"></div>
        <div class="relative">
            <div class="flex items-center justify-between mb-4">
                <div class="flex h-12 w-12 items-center justify-center rounded-xl bg-white bg-opacity-20">
                    <i class="fas fa-user-graduate text-2xl"></i>
                </div>
                <div class="text-right">
                    <div class="text-sm opacity-80">This Month</div>
                    <div class="flex items-center text-sm">
                        <i class="fas fa-arrow-up mr-1"></i>
                        <span>+4.35%</span>
                    </div>
                </div>
            </div>
            <div>
                <h3 class="text-3xl font-bold mb-1"><?= number_format($stats['total_students'] ?? 0) ?></h3>
                <p class="text-blue-100">Total Students</p>
            </div>
        </div>
    </div>

    <!-- Total Staff Card -->
    <div class="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-green-500 to-green-600 p-6 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="absolute top-0 right-0 -mt-4 -mr-4 h-16 w-16 rounded-full bg-white bg-opacity-20"></div>
        <div class="relative">
            <div class="flex items-center justify-between mb-4">
                <div class="flex h-12 w-12 items-center justify-center rounded-xl bg-white bg-opacity-20">
                    <i class="fas fa-chalkboard-teacher text-2xl"></i>
                </div>
                <div class="text-right">
                    <div class="text-sm opacity-80">This Month</div>
                    <div class="flex items-center text-sm">
                        <i class="fas fa-arrow-up mr-1"></i>
                        <span>+2.59%</span>
                    </div>
                </div>
            </div>
            <div>
                <h3 class="text-3xl font-bold mb-1"><?= number_format($stats['total_staff'] ?? 0) ?></h3>
                <p class="text-green-100">Total Staff</p>
            </div>
        </div>
    </div>

    <!-- Total Classes Card -->
    <div class="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-500 to-purple-600 p-6 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="absolute top-0 right-0 -mt-4 -mr-4 h-16 w-16 rounded-full bg-white bg-opacity-20"></div>
        <div class="relative">
            <div class="flex items-center justify-between mb-4">
                <div class="flex h-12 w-12 items-center justify-center rounded-xl bg-white bg-opacity-20">
                    <i class="fas fa-school text-2xl"></i>
                </div>
                <div class="text-right">
                    <div class="text-sm opacity-80">This Month</div>
                    <div class="flex items-center text-sm">
                        <i class="fas fa-arrow-down mr-1"></i>
                        <span>-0.95%</span>
                    </div>
                </div>
            </div>
            <div>
                <h3 class="text-3xl font-bold mb-1"><?= number_format($stats['total_classes'] ?? 0) ?></h3>
                <p class="text-purple-100">Total Classes</p>
            </div>
        </div>
    </div>

    <!-- Monthly Expenses Card -->
    <div class="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-orange-500 to-orange-600 p-6 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
        <div class="absolute top-0 right-0 -mt-4 -mr-4 h-16 w-16 rounded-full bg-white bg-opacity-20"></div>
        <div class="relative">
            <div class="flex items-center justify-between mb-4">
                <div class="flex h-12 w-12 items-center justify-center rounded-xl bg-white bg-opacity-20">
                    <i class="fas fa-money-bill-wave text-2xl"></i>
                </div>
                <div class="text-right">
                    <div class="text-sm opacity-80">This Month</div>
                    <div class="flex items-center text-sm">
                        <i class="fas fa-arrow-down mr-1"></i>
                        <span>-1.10%</span>
                    </div>
                </div>
            </div>
            <div>
                <h3 class="text-3xl font-bold mb-1">$<?= number_format($stats['monthly_expenses'] ?? 0, 2) ?></h3>
                <p class="text-orange-100">Monthly Expenses</p>
            </div>
        </div>
    </div>
</div>

<!-- Workflow Status Cards -->
<?php if (!empty($pending_workflows)): ?>
<div class="mb-8">
    <div class="flex items-center justify-between mb-6">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Pending Approvals</h2>
        <a href="<?= base_url('admin/workflows') ?>" class="text-primary hover:text-primary-dark transition-colors duration-200">
            View All <i class="fas fa-arrow-right ml-1"></i>
        </a>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <?php foreach (array_slice($pending_workflows, 0, 3) as $workflow): ?>
            <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="h-10 w-10 rounded-full bg-yellow-100 dark:bg-yellow-900 flex items-center justify-center">
                            <i class="fas fa-clock text-yellow-600 dark:text-yellow-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="font-semibold text-gray-900 dark:text-white"><?= esc($workflow['workflow_type']) ?></h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Step: <?= esc($workflow['step']) ?></p>
                        </div>
                    </div>
                    <span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
                        Pending
                    </span>
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-300 mb-4">
                    Created: <?= date('M j, Y', strtotime($workflow['workflow_created'])) ?>
                </div>
                <div class="flex space-x-2">
                    <button class="flex-1 bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                        <i class="fas fa-check mr-1"></i>Approve
                    </button>
                    <button class="flex-1 bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                        <i class="fas fa-times mr-1"></i>Reject
                    </button>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
</div>
<?php endif; ?>

<!-- Charts Section -->
<div class="mt-4 grid grid-cols-12 gap-4 md:mt-6 md:gap-6 2xl:mt-7.5 2xl:gap-7.5">
    <!-- Enrollment Chart -->
    <div class="col-span-12 xl:col-span-8">
        <div class="rounded-sm border border-stroke bg-white px-5 pt-7.5 pb-5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5">
            <div class="flex flex-wrap items-start justify-between gap-3 sm:flex-nowrap">
                <div class="flex w-full flex-wrap gap-3 sm:gap-5">
                    <div class="flex min-w-47.5">
                        <span class="mt-1 mr-2 flex h-4 w-full max-w-4 items-center justify-center rounded-full border border-primary">
                            <span class="block h-2.5 w-full max-w-2.5 rounded-full bg-primary"></span>
                        </span>
                        <div class="w-full">
                            <p class="font-semibold text-primary">Total Enrollments</p>
                            <p class="text-sm font-medium">12.04.2022 - 12.05.2022</p>
                        </div>
                    </div>
                </div>
                <div class="flex w-full max-w-45 justify-end">
                    <div class="inline-flex items-center rounded-md bg-whiter p-1.5 dark:bg-meta-4">
                        <button class="rounded bg-white py-1 px-3 text-xs font-medium text-black shadow-card hover:bg-white hover:shadow-card dark:bg-boxdark dark:text-white dark:hover:bg-boxdark">
                            Day
                        </button>
                        <button class="rounded py-1 px-3 text-xs font-medium text-black hover:bg-white hover:shadow-card dark:text-white dark:hover:bg-boxdark">
                            Week
                        </button>
                        <button class="rounded py-1 px-3 text-xs font-medium text-black hover:bg-white hover:shadow-card dark:text-white dark:hover:bg-boxdark">
                            Month
                        </button>
                    </div>
                </div>
            </div>

            <div>
                <div id="chartOne" class="-ml-5 h-[355px] w-[105%]">
                    <canvas id="enrollmentChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Expenses Chart -->
    <div class="col-span-12 xl:col-span-4">
        <div class="rounded-sm border border-stroke bg-white px-5 pt-7.5 pb-5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5">
            <div class="mb-3 justify-between gap-4 sm:flex">
                <div>
                    <h5 class="text-xl font-semibold text-black dark:text-white">
                        Expenses Analytics
                    </h5>
                </div>
                <div>
                    <div class="relative z-20 inline-block">
                        <select class="relative z-20 inline-flex appearance-none bg-transparent py-1 pl-3 pr-8 text-sm font-medium outline-none">
                            <option value="">Monthly</option>
                            <option value="">Yearly</option>
                        </select>
                        <span class="absolute top-1/2 right-3 z-10 -translate-y-1/2">
                            <i class="fas fa-chevron-down text-sm"></i>
                        </span>
                    </div>
                </div>
            </div>

            <div class="mb-2">
                <div id="chartThree" class="mx-auto flex justify-center">
                    <canvas id="expensesChart" width="200" height="200"></canvas>
                </div>
            </div>

            <div class="-mx-8 flex flex-wrap items-center justify-center gap-y-3">
                <div class="w-full px-8 sm:w-1/2">
                    <div class="flex w-full items-center">
                        <span class="mr-2 block h-3 w-full max-w-3 rounded-full bg-primary"></span>
                        <p class="flex w-full justify-between text-sm font-medium text-black dark:text-white">
                            <span> Office Supplies </span>
                            <span> 65% </span>
                        </p>
                    </div>
                </div>
                <div class="w-full px-8 sm:w-1/2">
                    <div class="flex w-full items-center">
                        <span class="mr-2 block h-3 w-full max-w-3 rounded-full bg-[#6577F3]"></span>
                        <p class="flex w-full justify-between text-sm font-medium text-black dark:text-white">
                            <span> Utilities </span>
                            <span> 34% </span>
                        </p>
                    </div>
                </div>
                <div class="w-full px-8 sm:w-1/2">
                    <div class="flex w-full items-center">
                        <span class="mr-2 block h-3 w-full max-w-3 rounded-full bg-[#8FD0EF]"></span>
                        <p class="flex w-full justify-between text-sm font-medium text-black dark:text-white">
                            <span> Maintenance </span>
                            <span> 45% </span>
                        </p>
                    </div>
                </div>
                <div class="w-full px-8 sm:w-1/2">
                    <div class="flex w-full items-center">
                        <span class="mr-2 block h-3 w-full max-w-3 rounded-full bg-[#0FADCF]"></span>
                        <p class="flex w-full justify-between text-sm font-medium text-black dark:text-white">
                            <span> Other </span>
                            <span> 12% </span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="col-span-12 mt-4 md:mt-6 2xl:mt-7.5">
    <div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
            <h3 class="font-medium text-black dark:text-white">Recent Activities</h3>
        </div>
        <div class="p-7">
            <?php if (!empty($recent_activities)): ?>
                <div class="flex flex-col gap-5">
                    <?php foreach ($recent_activities as $index => $activity): ?>
                        <div class="flex items-center gap-3">
                            <div class="flex h-9 w-9 items-center justify-center rounded-full border-[0.5px] border-stroke bg-gray dark:border-strokedark dark:bg-meta-4">
                                <i class="<?= $activity['icon'] ?> <?= $activity['color'] ?> text-sm"></i>
                            </div>
                            <div class="flex flex-1 items-center justify-between">
                                <div>
                                    <h5 class="font-medium text-black dark:text-white">
                                        <?= esc($activity['description']) ?>
                                    </h5>
                                    <p class="text-sm text-body">
                                        <?= date('M j, Y \a\t g:i A', strtotime($activity['date'])) ?>
                                    </p>
                                </div>
                                <div class="flex h-6 w-6 items-center justify-center rounded-full bg-primary">
                                    <span class="text-sm font-medium text-white"><?= $index + 1 ?></span>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="text-center py-8">
                    <div class="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-gray">
                        <i class="fas fa-inbox text-gray-400 text-xl"></i>
                    </div>
                    <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No activities</h3>
                    <p class="mt-1 text-sm text-gray-500">Get started by creating your first activity.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="mt-4 grid grid-cols-12 gap-4 md:mt-6 md:gap-6 2xl:mt-7.5 2xl:gap-7.5">
    <div class="col-span-12">
        <div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
            <div class="border-b border-stroke py-4 px-7 dark:border-strokedark">
                <h3 class="font-medium text-black dark:text-white">Quick Actions</h3>
            </div>
            <div class="p-7">
                <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 xl:grid-cols-4">
                    <a href="<?= base_url('admin/students') ?>" class="rounded-sm border border-stroke bg-white p-7.5 shadow-default transition-all duration-300 hover:shadow-lg dark:border-strokedark dark:bg-boxdark">
                        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
                            <i class="fas fa-user-plus text-primary text-xl"></i>
                        </div>
                        <div class="mt-4">
                            <h4 class="text-title-md font-bold text-black dark:text-white">
                                Add Student
                            </h4>
                            <p class="mt-1 text-sm">Register a new student in the system</p>
                        </div>
                    </a>

                    <a href="<?= base_url('admin/staff') ?>" class="rounded-sm border border-stroke bg-white p-7.5 shadow-default transition-all duration-300 hover:shadow-lg dark:border-strokedark dark:bg-boxdark">
                        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
                            <i class="fas fa-user-tie text-primary text-xl"></i>
                        </div>
                        <div class="mt-4">
                            <h4 class="text-title-md font-bold text-black dark:text-white">
                                Add Staff
                            </h4>
                            <p class="mt-1 text-sm">Add new staff member to the system</p>
                        </div>
                    </a>

                    <a href="<?= base_url('admin/fees') ?>" class="rounded-sm border border-stroke bg-white p-7.5 shadow-default transition-all duration-300 hover:shadow-lg dark:border-strokedark dark:bg-boxdark">
                        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
                            <i class="fas fa-dollar-sign text-primary text-xl"></i>
                        </div>
                        <div class="mt-4">
                            <h4 class="text-title-md font-bold text-black dark:text-white">
                                Manage Fees
                            </h4>
                            <p class="mt-1 text-sm">Set up and manage fee structures</p>
                        </div>
                    </a>

                    <a href="<?= base_url('admin/reports') ?>" class="rounded-sm border border-stroke bg-white p-7.5 shadow-default transition-all duration-300 hover:shadow-lg dark:border-strokedark dark:bg-boxdark">
                        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
                            <i class="fas fa-chart-line text-primary text-xl"></i>
                        </div>
                        <div class="mt-4">
                            <h4 class="text-title-md font-bold text-black dark:text-white">
                                View Reports
                            </h4>
                            <p class="mt-1 text-sm">Generate and view system reports</p>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Enhanced Dashboard JavaScript with Modern Features
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize dashboard components
        initializeQuickActions();
        initializeNotifications();
        initializeCharts();
        initializeRealTimeUpdates();
        initializeAnimations();
    });

    // Quick Actions Dropdown
    function initializeQuickActions() {
        const quickActionsBtn = document.getElementById('quickActionsBtn');
        const quickActionsMenu = document.getElementById('quickActionsMenu');

        if (quickActionsBtn && quickActionsMenu) {
            quickActionsBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                quickActionsMenu.classList.toggle('hidden');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function() {
                quickActionsMenu.classList.add('hidden');
            });
        }
    }

    // Notification System
    function initializeNotifications() {
        const notificationBtn = document.getElementById('notificationBtn');

        if (notificationBtn) {
            notificationBtn.addEventListener('click', function() {
                // Show notifications panel or redirect to notifications page
                showNotificationsPanel();
            });
        }
    }

    function showNotificationsPanel() {
        // Create and show notifications panel
        const panel = document.createElement('div');
        panel.className = 'fixed inset-0 z-50 overflow-hidden';
        panel.innerHTML = `
            <div class="absolute inset-0 bg-black bg-opacity-50" onclick="this.parentElement.remove()"></div>
            <div class="absolute right-0 top-0 h-full w-96 bg-white dark:bg-gray-800 shadow-xl transform transition-transform duration-300 translate-x-full">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Notifications</h3>
                        <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div id="notificationsList" class="space-y-4">
                        <div class="text-center text-gray-500 dark:text-gray-400">
                            <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                            <p>Loading notifications...</p>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(panel);

        // Animate panel in
        setTimeout(() => {
            panel.querySelector('.w-96').classList.remove('translate-x-full');
        }, 10);

        // Load notifications
        loadNotifications();
    }

    function loadNotifications() {
        fetch('<?= base_url('admin/notifications') ?>')
            .then(response => response.json())
            .then(data => {
                const notificationsList = document.getElementById('notificationsList');
                if (data.notifications && data.notifications.length > 0) {
                    notificationsList.innerHTML = data.notifications.map(notification => `
                        <div class="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-${notification.icon || 'bell'} text-blue-500"></i>
                                </div>
                                <div class="ml-3 flex-1">
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">
                                        ${notification.title}
                                    </p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">
                                        ${notification.message}
                                    </p>
                                    <p class="text-xs text-gray-400 mt-1">
                                        ${notification.created_at}
                                    </p>
                                </div>
                            </div>
                        </div>
                    `).join('');
                } else {
                    notificationsList.innerHTML = `
                        <div class="text-center text-gray-500 dark:text-gray-400">
                            <i class="fas fa-bell-slash text-2xl mb-2"></i>
                            <p>No notifications</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error loading notifications:', error);
                document.getElementById('notificationsList').innerHTML = `
                    <div class="text-center text-red-500">
                        <i class="fas fa-exclamation-triangle text-2xl mb-2"></i>
                        <p>Error loading notifications</p>
                    </div>
                `;
            });
    }

    // Initialize Charts
    function initializeCharts() {
        // Only initialize if Chart.js is available and elements exist
        if (typeof Chart === 'undefined') {
            console.warn('Chart.js not loaded');
            return;
        }

        const enrollmentCtx = document.getElementById('enrollmentChart');
        const expensesCtx = document.getElementById('expensesChart');

        if (enrollmentCtx) {
            initializeEnrollmentChart(enrollmentCtx);
        }

        if (expensesCtx) {
            initializeExpensesChart(expensesCtx);
        }
    }

    function initializeEnrollmentChart(ctx) {
        const enrollmentData = <?= json_encode($charts_data['monthly_admissions'] ?? []) ?>;
        const monthlyData = new Array(12).fill(0);

        enrollmentData.forEach(item => {
            if (item.month && item.count) {
                monthlyData[item.month - 1] = item.count;
            }
        });

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                datasets: [{
                    label: 'Enrollments',
                    data: monthlyData,
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4,
                    fill: true,
                    pointBackgroundColor: 'rgb(59, 130, 246)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: 'rgb(59, 130, 246)',
                        borderWidth: 1
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        ticks: {
                            color: '#6B7280'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#6B7280'
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });
    }

    function initializeExpensesChart(ctx) {
        const expensesData = <?= json_encode($charts_data['monthly_expenses'] ?? []) ?>;
        const expenseLabels = expensesData.map(item => item.month ? 'Month ' + item.month : 'Unknown');
        const expenseValues = expensesData.map(item => parseFloat(item.total) || 0);

        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: expenseLabels,
                datasets: [{
                    data: expenseValues,
                    backgroundColor: [
                        'rgba(239, 68, 68, 0.8)',
                        'rgba(34, 197, 94, 0.8)',
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(168, 85, 247, 0.8)',
                        'rgba(245, 158, 11, 0.8)',
                        'rgba(236, 72, 153, 0.8)'
                    ],
                    borderWidth: 3,
                    borderColor: '#fff',
                    hoverBorderWidth: 4,
                    hoverOffset: 10
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: {
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${label}: $${value.toLocaleString()} (${percentage}%)`;
                            }
                        }
                    }
                },
                animation: {
                    animateRotate: true,
                    animateScale: true,
                    duration: 1000
                }
            }
        });
    }

    // Real-time Updates
    function initializeRealTimeUpdates() {
        // Update dashboard data every 30 seconds
        setInterval(updateDashboardData, 30000);
    }

    function updateDashboardData() {
        fetch('<?= base_url('admin/dashboard-updates') ?>')
            .then(response => response.json())
            .then(data => {
                // Update notification count
                const notificationBtn = document.getElementById('notificationBtn');
                if (notificationBtn && data.notification_count) {
                    const badge = notificationBtn.querySelector('span');
                    if (badge) {
                        badge.textContent = data.notification_count;
                        badge.style.display = data.notification_count > 0 ? 'flex' : 'none';
                    }
                }

                // Show toast for new notifications
                if (data.notifications && data.notifications.length > 0) {
                    showToast('New notifications received', 'info');
                }
            })
            .catch(error => {
                console.error('Error updating dashboard:', error);
            });
    }

    // Animations
    function initializeAnimations() {
        // Animate stats cards on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in-up');
                }
            });
        }, observerOptions);

        // Observe all stat cards
        document.querySelectorAll('.group').forEach(card => {
            observer.observe(card);
        });
    }

    // Toast Notifications
    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full ${getToastClasses(type)}`;
        toast.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-${getToastIcon(type)} mr-3"></i>
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        document.body.appendChild(toast);

        // Animate in
        setTimeout(() => {
            toast.classList.remove('translate-x-full');
        }, 10);

        // Auto remove after 5 seconds
        setTimeout(() => {
            toast.classList.add('translate-x-full');
            setTimeout(() => toast.remove(), 300);
        }, 5000);
    }

    function getToastClasses(type) {
        const classes = {
            'info': 'bg-blue-500 text-white',
            'success': 'bg-green-500 text-white',
            'warning': 'bg-yellow-500 text-white',
            'error': 'bg-red-500 text-white'
        };
        return classes[type] || classes.info;
    }

    function getToastIcon(type) {
        const icons = {
            'info': 'info-circle',
            'success': 'check-circle',
            'warning': 'exclamation-triangle',
            'error': 'exclamation-circle'
        };
        return icons[type] || icons.info;
    }
</script>

<!-- Add custom CSS for animations -->
<style>
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate-fade-in-up {
        animation: fadeInUp 0.6s ease-out forwards;
    }

    /* Smooth hover effects for cards */
    .group:hover .absolute {
        transform: scale(1.1);
        transition: transform 0.3s ease;
    }

    /* Loading skeleton animation */
    @keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.5;
        }
    }

    .animate-pulse {
        animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }
</style>
<?= $this->endSection() ?>
