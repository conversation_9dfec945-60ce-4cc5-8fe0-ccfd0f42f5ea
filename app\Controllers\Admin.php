<?php

namespace App\Controllers;

use App\Models\DashboardModel;
use App\Libraries\NotificationService;
use App\Libraries\WorkflowEngine;

class Admin extends BaseController
{
    protected $dashboardModel;
    protected $notificationService;
    protected $workflowEngine;
    protected $cache;

    public function __construct()
    {
        $this->dashboardModel = new DashboardModel();
        $this->notificationService = new NotificationService();
        $this->workflowEngine = new WorkflowEngine();
        $this->cache = \Config\Services::cache();
    }

    public function index()
    {
        return $this->dashboard();
    }

    public function dashboard()
    {
        // Check cache first for performance
        $cacheKey = 'dashboard_data_' . auth()->id() . '_' . date('Y-m-d-H');
        $cachedData = $this->cache->get($cacheKey);

        if ($cachedData) {
            $data = $cachedData;
            $data['cache_hit'] = true;
        } else {
            $data = [
                'title' => 'Dashboard',
                'stats' => $this->dashboardModel->getDashboardStats(),
                'recent_activities' => $this->dashboardModel->getRecentActivities(),
                'charts_data' => $this->dashboardModel->getChartsData(),
                'notifications' => $this->notificationService->getUnreadNotifications(auth()->id(), 5),
                'notification_stats' => $this->notificationService->getNotificationStats(auth()->id()),
                'pending_workflows' => $this->getPendingWorkflows(),
                'cache_hit' => false
            ];

            // Cache for 1 hour
            $this->cache->save($cacheKey, $data, 3600);
        }

        return view('admin/dashboard', $data);
    }

    /**
     * Get pending workflows for current user
     */
    private function getPendingWorkflows(): array
    {
        $db = \Config\Database::connect();
        $builder = $db->table('workflow_assignments wa');

        $workflows = $builder
            ->select('wa.*, w.workflow_type, w.data, w.created_at as workflow_created')
            ->join('workflows w', 'w.id = wa.workflow_id')
            ->where('wa.user_id', auth()->id())
            ->where('wa.status', 'pending')
            ->orderBy('w.created_at', 'DESC')
            ->limit(10)
            ->get()
            ->getResultArray();

        return $workflows;
    }

    /**
     * AJAX endpoint for real-time dashboard updates
     */
    public function dashboardUpdates()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(400);
        }

        $data = [
            'notifications' => $this->notificationService->getUnreadNotifications(auth()->id(), 5),
            'notification_count' => $this->notificationService->getNotificationStats(auth()->id())['unread'],
            'pending_workflows' => $this->getPendingWorkflows(),
            'timestamp' => time()
        ];

        return $this->response->setJSON($data);
    }

    /**
     * Mark notification as read
     */
    public function markNotificationRead($notificationId)
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(400);
        }

        $result = $this->notificationService->markAsRead($notificationId, auth()->id());

        return $this->response->setJSON([
            'success' => $result,
            'message' => $result ? 'Notification marked as read' : 'Failed to mark notification as read'
        ]);
    }

    /**
     * Mark all notifications as read
     */
    public function markAllNotificationsRead()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(400);
        }

        $result = $this->notificationService->markAllAsRead(auth()->id());

        return $this->response->setJSON([
            'success' => $result,
            'message' => $result ? 'All notifications marked as read' : 'Failed to mark notifications as read'
        ]);
    }

    public function students()
    {
        $data = [
            'title' => 'Students Management',
            'students' => $this->dashboardModel->getStudents()
        ];

        return view('admin/students', $data);
    }

    public function staff()
    {
        $data = [
            'title' => 'Staff Management',
            'staff' => $this->dashboardModel->getStaff()
        ];

        return view('admin/staff', $data);
    }

    public function classes()
    {
        $data = [
            'title' => 'Classes Management',
            'classes' => $this->dashboardModel->getClasses()
        ];

        return view('admin/classes', $data);
    }

    public function fees()
    {
        $data = [
            'title' => 'Fees Management',
            'fees' => $this->dashboardModel->getFees()
        ];

        return view('admin/fees', $data);
    }

    public function expenses()
    {
        $data = [
            'title' => 'Expenses Management',
            'expenses' => $this->dashboardModel->getExpenses()
        ];

        return view('admin/expenses', $data);
    }

    public function reports()
    {
        $data = [
            'title' => 'Reports',
            'reports_data' => $this->dashboardModel->getReportsData()
        ];

        return view('admin/reports', $data);
    }

    public function settings()
    {
        $data = [
            'title' => 'Settings',
            'settings' => $this->dashboardModel->getSettings()
        ];

        return view('admin/settings', $data);
    }
}
