<?php

namespace Config;

use CodeIgniter\Config\Filters as BaseFilters;
use CodeIgniter\Filters\Cors;
use CodeIgniter\Filters\CSRF;
use CodeIgniter\Filters\DebugToolbar;
use CodeIgniter\Filters\ForceHTTPS;
use CodeIgniter\Filters\Honeypot;
use CodeIgniter\Filters\InvalidChars;
use CodeIgniter\Filters\PageCache;
use CodeIgniter\Filters\PerformanceMetrics;
use CodeIgniter\Filters\SecureHeaders;

class Filters extends BaseFilters
{
    /**
     * Configures aliases for Filter classes to
     * make reading things nicer and simpler.
     *
     * @var array<string, class-string|list<class-string>>
     *
     * [filter_name => classname]
     * or [filter_name => [classname1, classname2, ...]]
     */
    public array $aliases = [
        'csrf'          => CSRF::class,
        'toolbar'       => DebugToolbar::class,
        'honeypot'      => Honeypot::class,
        'invalidchars'  => InvalidChars::class,
        'secureheaders' => SecureHeaders::class,
        'cors'          => Cors::class,
        'forcehttps'    => ForceHTTPS::class,
        'pagecache'     => PageCache::class,
        'performance'   => PerformanceMetrics::class,
        // Custom filters for optimization
        'auth'          => \App\Filters\AuthFilter::class,
        'permission'    => \App\Filters\PermissionFilter::class,
        'role'          => \App\Filters\RoleFilter::class,
        'throttle'      => \App\Filters\ThrottleFilter::class,
        'cache'         => \App\Filters\CacheFilter::class,
    ];

    /**
     * List of special required filters.
     *
     * The filters listed here are special. They are applied before and after
     * other kinds of filters, and always applied even if a route does not exist.
     *
     * Filters set by default provide framework functionality. If removed,
     * those functions will no longer work.
     *
     * @see https://codeigniter.com/user_guide/incoming/filters.html#provided-filters
     *
     * @var array{before: list<string>, after: list<string>}
     */
    public array $required = [
        'before' => [
            'forcehttps', // Force Global Secure Requests
            'pagecache',  // Web Page Caching
        ],
        'after' => [
            'pagecache',   // Web Page Caching
            'performance', // Performance Metrics
            'toolbar',     // Debug Toolbar
        ],
    ];

    /**
     * List of filter aliases that are always
     * applied before and after every request.
     *
     * @var array<string, array<string, array<string, string>>>|array<string, list<string>>
     */
    public array $globals = [
        'before' => [
            // 'honeypot',
            // 'csrf',
            // 'invalidchars',
        ],
        'after' => [
            // 'honeypot',
            // 'secureheaders',
        ],
    ];

    /**
     * List of filter aliases that works on a
     * particular HTTP method (GET, POST, etc.).
     *
     * Example:
     * 'POST' => ['foo', 'bar']
     *
     * If you use this, you should disable auto-routing because auto-routing
     * permits any HTTP method to access a controller. Accessing the controller
     * with a method you don't expect could bypass the filter.
     *
     * @var array<string, list<string>>
     */
    public array $methods = [];

    /**
     * List of filter aliases that should run on any
     * before or after URI patterns.
     *
     * Example:
     * 'isLoggedIn' => ['before' => ['account/*', 'profiles/*']]
     *
     * @var array<string, array<string, list<string>>>
     */
    public array $filters = [
        // Authentication required for admin area
        'auth' => [
            'before' => ['admin/*', 'api/*']
        ],

        // Permission-based access control
        'permission' => [
            'before' => [
                'admin/students/create' => ['students.create'],
                'admin/students/edit/*' => ['students.edit'],
                'admin/students/delete/*' => ['students.delete'],
                'admin/staff/create' => ['admin.users'],
                'admin/staff/edit/*' => ['admin.users'],
                'admin/settings/*' => ['admin.settings'],
                'admin/reports/*' => ['reports.view'],
            ]
        ],

        // Role-based access control
        'role' => [
            'before' => [
                'admin/settings/*' => ['superadmin', 'admin'],
                'admin/users/*' => ['superadmin', 'admin'],
                'admin/system/*' => ['superadmin'],
            ]
        ],

        // Rate limiting for API and sensitive endpoints
        'throttle' => [
            'before' => [
                'auth/login' => [5, 300],    // 5 attempts per 5 minutes
                'auth/register' => [3, 3600], // 3 registrations per hour
                'api/*' => [100, 3600],       // 100 API calls per hour
                'admin/ajax/*' => [200, 3600] // 200 AJAX calls per hour
            ]
        ],

        // Caching for performance
        'cache' => [
            'before' => [
                'admin/dashboard' => [300],    // Cache dashboard for 5 minutes
                'admin/reports/view/*' => [600], // Cache reports for 10 minutes
                'admin/students/index' => [180], // Cache student list for 3 minutes
            ]
        ]
    ];
}
