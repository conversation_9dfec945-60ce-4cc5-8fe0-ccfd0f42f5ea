<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

/**
 * Smart Caching Filter
 * Provides intelligent caching for improved performance
 */
class CacheFilter implements FilterInterface
{
    private $cache;
    
    public function __construct()
    {
        $this->cache = \Config\Services::cache();
    }

    /**
     * Do whatever processing this filter needs to do.
     *
     * @param RequestInterface $request
     * @param array|null       $arguments Expected format: [ttl, cacheKey] e.g., [3600, 'dashboard_data']
     *
     * @return RequestInterface|ResponseInterface|string|void
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        // Only cache GET requests
        if ($request->getMethod() !== 'GET') {
            return;
        }
        
        // Don't cache if user-specific data or admin actions
        if ($this->shouldSkipCache($request)) {
            return;
        }
        
        $ttl = $arguments[0] ?? 300; // Default 5 minutes
        $cacheKey = $this->generateCacheKey($request, $arguments[1] ?? null);
        
        // Try to get cached response
        $cachedResponse = $this->cache->get($cacheKey);
        
        if ($cachedResponse !== null) {
            // Log cache hit
            log_message('debug', "Cache hit for key: {$cacheKey}");
            
            // Return cached response
            return service('response')
                ->setBody($cachedResponse['body'])
                ->setHeader('Content-Type', $cachedResponse['content_type'])
                ->setHeader('X-Cache', 'HIT')
                ->setHeader('X-Cache-Key', $cacheKey);
        }
        
        // Store cache info for after filter
        $request->cacheInfo = [
            'key' => $cacheKey,
            'ttl' => $ttl
        ];
    }

    /**
     * Allows After filters to inspect and modify the response
     * object as needed.
     *
     * @param RequestInterface  $request
     * @param ResponseInterface $response
     * @param array|null        $arguments
     *
     * @return ResponseInterface|void
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Only cache successful responses
        if ($response->getStatusCode() !== 200) {
            return $response;
        }
        
        // Check if we should cache this response
        if (!isset($request->cacheInfo) || $this->shouldSkipCache($request)) {
            return $response;
        }
        
        $cacheInfo = $request->cacheInfo;
        
        // Prepare cache data
        $cacheData = [
            'body' => $response->getBody(),
            'content_type' => $response->getHeaderLine('Content-Type'),
            'cached_at' => time()
        ];
        
        // Save to cache
        $this->cache->save($cacheInfo['key'], $cacheData, $cacheInfo['ttl']);
        
        // Add cache headers
        $response->setHeader('X-Cache', 'MISS');
        $response->setHeader('X-Cache-Key', $cacheInfo['key']);
        
        log_message('debug', "Cached response for key: {$cacheInfo['key']}");
        
        return $response;
    }

    /**
     * Generate cache key based on request
     */
    private function generateCacheKey(RequestInterface $request, ?string $customKey = null): string
    {
        if ($customKey) {
            return "page_cache_{$customKey}";
        }
        
        $uri = $request->getUri();
        $path = $uri->getPath();
        $query = $uri->getQuery();
        
        // Include user role in cache key for role-specific content
        $userRole = 'guest';
        if (auth()->loggedIn()) {
            $user = auth()->user();
            $groups = $user->getGroups();
            $userRole = !empty($groups) ? $groups[0] : 'user';
        }
        
        $key = "page_cache_" . md5($path . $query . $userRole);
        
        return $key;
    }

    /**
     * Determine if caching should be skipped
     */
    private function shouldSkipCache(RequestInterface $request): bool
    {
        $uri = $request->getUri()->getPath();
        
        // Skip caching for these patterns
        $skipPatterns = [
            '/admin/api/',
            '/admin/ajax/',
            '/auth/',
            '/admin/notifications',
            '/admin/profile',
            '/admin/settings'
        ];
        
        foreach ($skipPatterns as $pattern) {
            if (strpos($uri, $pattern) !== false) {
                return true;
            }
        }
        
        // Skip if there are POST parameters (form submissions)
        if (!empty($request->getPost())) {
            return true;
        }
        
        // Skip if there are specific headers indicating dynamic content
        if ($request->hasHeader('X-No-Cache')) {
            return true;
        }
        
        return false;
    }

    /**
     * Clear cache for specific patterns
     */
    public static function clearCache(string $pattern = null): bool
    {
        $cache = \Config\Services::cache();
        
        if ($pattern) {
            // Clear specific cache pattern
            return $cache->deleteMatching("page_cache_{$pattern}*");
        }
        
        // Clear all page cache
        return $cache->deleteMatching('page_cache_*');
    }
}
