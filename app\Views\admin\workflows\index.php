<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<!-- Enhanced Workflow Management Header -->
<div class="mb-8">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div class="mb-4 lg:mb-0">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                Workflow Management
            </h1>
            <p class="text-gray-600 dark:text-gray-400">
                Manage and track all business process workflows
            </p>
        </div>
        
        <div class="flex items-center space-x-4">
            <button id="startWorkflowBtn" class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors duration-200">
                <i class="fas fa-plus mr-2"></i>
                Start Workflow
            </button>
            
            <div class="relative">
                <button id="filterBtn" class="inline-flex items-center px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200">
                    <i class="fas fa-filter mr-2"></i>
                    Filter
                    <i class="fas fa-chevron-down ml-2"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Workflow Statistics -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
        <div class="flex items-center">
            <div class="flex h-12 w-12 items-center justify-center rounded-xl bg-blue-100 dark:bg-blue-900">
                <i class="fas fa-tasks text-blue-600 dark:text-blue-400 text-xl"></i>
            </div>
            <div class="ml-4">
                <h3 class="text-2xl font-bold text-gray-900 dark:text-white"><?= number_format($workflow_stats['total'] ?? 0) ?></h3>
                <p class="text-gray-600 dark:text-gray-400">Total Workflows</p>
            </div>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
        <div class="flex items-center">
            <div class="flex h-12 w-12 items-center justify-center rounded-xl bg-yellow-100 dark:bg-yellow-900">
                <i class="fas fa-clock text-yellow-600 dark:text-yellow-400 text-xl"></i>
            </div>
            <div class="ml-4">
                <h3 class="text-2xl font-bold text-gray-900 dark:text-white"><?= number_format($workflow_stats['active'] ?? 0) ?></h3>
                <p class="text-gray-600 dark:text-gray-400">Active Workflows</p>
            </div>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
        <div class="flex items-center">
            <div class="flex h-12 w-12 items-center justify-center rounded-xl bg-green-100 dark:bg-green-900">
                <i class="fas fa-check-circle text-green-600 dark:text-green-400 text-xl"></i>
            </div>
            <div class="ml-4">
                <h3 class="text-2xl font-bold text-gray-900 dark:text-white"><?= number_format($workflow_stats['completed'] ?? 0) ?></h3>
                <p class="text-gray-600 dark:text-gray-400">Completed</p>
            </div>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
        <div class="flex items-center">
            <div class="flex h-12 w-12 items-center justify-center rounded-xl bg-red-100 dark:bg-red-900">
                <i class="fas fa-exclamation-circle text-red-600 dark:text-red-400 text-xl"></i>
            </div>
            <div class="ml-4">
                <h3 class="text-2xl font-bold text-gray-900 dark:text-white"><?= number_format($pending_count ?? 0) ?></h3>
                <p class="text-gray-600 dark:text-gray-400">Pending My Action</p>
            </div>
        </div>
    </div>
</div>

<!-- Workflows Table -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Recent Workflows</h2>
            <div class="flex items-center space-x-2">
                <button id="bulkActionBtn" class="hidden px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200">
                    <i class="fas fa-cog mr-1"></i>
                    Bulk Actions
                </button>
                <button id="refreshBtn" class="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors duration-200">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="w-full">
            <thead class="bg-gray-50 dark:bg-gray-700">
                <tr>
                    <th class="px-6 py-3 text-left">
                        <input type="checkbox" id="selectAll" class="rounded border-gray-300 text-primary focus:ring-primary">
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Workflow
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Type
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Status
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Current Step
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Created
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Actions
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                <?php if (!empty($workflows)): ?>
                    <?php foreach ($workflows as $workflow): ?>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                            <td class="px-6 py-4">
                                <input type="checkbox" class="workflow-checkbox rounded border-gray-300 text-primary focus:ring-primary" value="<?= $workflow['id'] ?>">
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="flex h-10 w-10 items-center justify-center rounded-full bg-primary bg-opacity-10">
                                        <i class="fas fa-file-alt text-primary"></i>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                                            Workflow #<?= $workflow['id'] ?>
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">
                                            ID: <?= $workflow['id'] ?>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                    <?= esc($workflow['workflow_type']) ?>
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <?php
                                $statusColors = [
                                    'active' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
                                    'completed' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
                                    'cancelled' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
                                    'on_hold' => 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                                ];
                                $statusClass = $statusColors[$workflow['status']] ?? $statusColors['active'];
                                ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $statusClass ?>">
                                    <?= ucfirst($workflow['status']) ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">
                                <?= esc($workflow['current_step']) ?>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                                <?= date('M j, Y', strtotime($workflow['created_at'])) ?>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-2">
                                    <button onclick="viewWorkflow(<?= $workflow['id'] ?>)" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <?php if ($workflow['assignment_status'] === 'pending'): ?>
                                        <button onclick="approveWorkflow(<?= $workflow['id'] ?>)" class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button onclick="rejectWorkflow(<?= $workflow['id'] ?>)" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    <?php endif; ?>
                                    <button onclick="showWorkflowHistory(<?= $workflow['id'] ?>)" class="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300">
                                        <i class="fas fa-history"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="7" class="px-6 py-12 text-center">
                            <div class="text-gray-500 dark:text-gray-400">
                                <i class="fas fa-inbox text-4xl mb-4"></i>
                                <p class="text-lg font-medium">No workflows found</p>
                                <p class="text-sm">Start your first workflow to get started</p>
                            </div>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Workflow management JavaScript
    document.addEventListener('DOMContentLoaded', function() {
        initializeWorkflowManagement();
    });

    function initializeWorkflowManagement() {
        // Initialize checkbox selection
        initializeCheckboxes();
        
        // Initialize action buttons
        initializeActionButtons();
        
        // Initialize refresh functionality
        initializeRefresh();
    }

    function initializeCheckboxes() {
        const selectAll = document.getElementById('selectAll');
        const checkboxes = document.querySelectorAll('.workflow-checkbox');
        const bulkActionBtn = document.getElementById('bulkActionBtn');

        selectAll?.addEventListener('change', function() {
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            toggleBulkActions();
        });

        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', toggleBulkActions);
        });

        function toggleBulkActions() {
            const checkedBoxes = document.querySelectorAll('.workflow-checkbox:checked');
            if (checkedBoxes.length > 0) {
                bulkActionBtn?.classList.remove('hidden');
            } else {
                bulkActionBtn?.classList.add('hidden');
            }
        }
    }

    function initializeActionButtons() {
        document.getElementById('startWorkflowBtn')?.addEventListener('click', showStartWorkflowModal);
        document.getElementById('bulkActionBtn')?.addEventListener('click', showBulkActionModal);
    }

    function initializeRefresh() {
        document.getElementById('refreshBtn')?.addEventListener('click', function() {
            location.reload();
        });
    }

    function viewWorkflow(workflowId) {
        // Show workflow details modal
        showWorkflowDetails(workflowId);
    }

    function approveWorkflow(workflowId) {
        processWorkflowAction(workflowId, 'approve');
    }

    function rejectWorkflow(workflowId) {
        processWorkflowAction(workflowId, 'reject');
    }

    function processWorkflowAction(workflowId, action) {
        if (!confirm(`Are you sure you want to ${action} this workflow?`)) {
            return;
        }

        fetch(`<?= base_url('admin/workflows/process-action') ?>/${workflowId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                action: action,
                action_data: {}
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(`Workflow ${action}d successfully`, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast(data.message || `Failed to ${action} workflow`, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast(`Error processing workflow action`, 'error');
        });
    }

    function showWorkflowHistory(workflowId) {
        fetch(`<?= base_url('admin/workflows/history') ?>/${workflowId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayWorkflowHistory(data.history);
                } else {
                    showToast('Failed to load workflow history', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('Error loading workflow history', 'error');
            });
    }

    function displayWorkflowHistory(history) {
        // Create and show history modal
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 z-50 overflow-y-auto';
        modal.innerHTML = `
            <div class="flex items-center justify-center min-h-screen px-4">
                <div class="fixed inset-0 bg-black opacity-50" onclick="this.parentElement.parentElement.remove()"></div>
                <div class="relative bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Workflow History</h3>
                        <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="space-y-4">
                        ${history.map(item => `
                            <div class="flex items-start space-x-3 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                <div class="flex-shrink-0">
                                    <div class="h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                                        <i class="fas fa-user text-blue-600 dark:text-blue-400 text-sm"></i>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center justify-between">
                                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">${item.username}</h4>
                                        <span class="text-xs text-gray-500 dark:text-gray-400">${new Date(item.created_at).toLocaleDateString()}</span>
                                    </div>
                                    <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                                        Action: <span class="font-medium">${item.action}</span>
                                    </p>
                                    <p class="text-sm text-gray-600 dark:text-gray-300">
                                        Step: <span class="font-medium">${item.step}</span>
                                    </p>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
    }

    function showToast(message, type = 'info') {
        // Toast notification implementation
        const toast = document.createElement('div');
        const bgColors = {
            'success': 'bg-green-500',
            'error': 'bg-red-500',
            'info': 'bg-blue-500',
            'warning': 'bg-yellow-500'
        };
        
        toast.className = `fixed top-4 right-4 z-50 p-4 rounded-lg text-white shadow-lg transform transition-all duration-300 translate-x-full ${bgColors[type] || bgColors.info}`;
        toast.innerHTML = `
            <div class="flex items-center">
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => toast.classList.remove('translate-x-full'), 10);
        setTimeout(() => {
            toast.classList.add('translate-x-full');
            setTimeout(() => toast.remove(), 300);
        }, 5000);
    }
</script>
<?= $this->endSection() ?>
