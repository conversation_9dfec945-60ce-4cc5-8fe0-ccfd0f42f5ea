<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

/**
 * Enhanced Authentication Filter
 * Provides optimized authentication checking with session management
 */
class AuthFilter implements FilterInterface
{
    /**
     * Do whatever processing this filter needs to do.
     * By default it should not return anything during
     * normal execution. However, when an abnormal state
     * is found, it should return an instance of
     * CodeIgniter\HTTP\Response. If it does, script
     * execution will end and that Response will be
     * sent back to the client, allowing for error pages,
     * redirects, etc.
     *
     * @param RequestInterface $request
     * @param array|null       $arguments
     *
     * @return RequestInterface|ResponseInterface|string|void
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        // Check if user is authenticated
        if (!auth()->loggedIn()) {
            // Store the intended URL for redirect after login
            session()->set('redirect_url', current_url());
            
            // If AJAX request, return JSON response
            if ($request->hasHeader('X-Requested-With') && $request->getHeaderLine('X-Requested-With') === 'XMLHttpRequest') {
                return service('response')->setJSON([
                    'success' => false,
                    'message' => 'Authentication required',
                    'redirect' => base_url('auth/login')
                ])->setStatusCode(401);
            }
            
            // Regular request, redirect to login
            return redirect()->to('auth/login')->with('error', 'Please log in to access this page.');
        }

        // Check if user account is active
        $user = auth()->user();
        if (!$user->isActivated()) {
            $this->logSecurityEvent($user->id, 'inactive_account_access_attempt', [
                'ip_address' => $request->getIPAddress(),
                'user_agent' => $request->getHeaderLine('User-Agent'),
                'requested_uri' => $request->getUri()->getPath()
            ], 'medium');

            auth()->logout();

            if ($request->hasHeader('X-Requested-With') && $request->getHeaderLine('X-Requested-With') === 'XMLHttpRequest') {
                return service('response')->setJSON([
                    'success' => false,
                    'message' => 'Account not activated',
                    'redirect' => base_url('auth/login')
                ])->setStatusCode(403);
            }

            return redirect()->to('auth/login')->with('error', 'Your account is not activated. Please check your email.');
        }

        // Check for specific permissions if provided
        if (!empty($arguments)) {
            foreach ($arguments as $permission) {
                if (!$user->can($permission)) {
                    $this->logSecurityEvent($user->id, 'insufficient_permissions', [
                        'permission' => $permission,
                        'requested_uri' => $request->getUri()->getPath(),
                        'user_roles' => $user->getGroups()
                    ], 'high');

                    if ($request->hasHeader('X-Requested-With') && $request->getHeaderLine('X-Requested-With') === 'XMLHttpRequest') {
                        return service('response')->setJSON([
                            'success' => false,
                            'message' => 'Insufficient permissions',
                            'required_permission' => $permission
                        ])->setStatusCode(403);
                    }

                    return redirect()->back()->with('error', 'You do not have permission to access this resource.');
                }
            }
        }

        // Check for suspicious activity patterns
        if ($this->detectSuspiciousActivity($user->id, $request->getIPAddress(), $request->getHeaderLine('User-Agent'))) {
            $this->logSecurityEvent($user->id, 'suspicious_activity_detected', [
                'ip_address' => $request->getIPAddress(),
                'user_agent' => $request->getHeaderLine('User-Agent'),
                'requested_uri' => $request->getUri()->getPath()
            ], 'critical');
        }

        // Update last activity timestamp for session management
        $this->updateLastActivity();
        
        // Check for session timeout (optional)
        if ($this->isSessionExpired()) {
            auth()->logout();
            
            if ($request->hasHeader('X-Requested-With') && $request->getHeaderLine('X-Requested-With') === 'XMLHttpRequest') {
                return service('response')->setJSON([
                    'success' => false,
                    'message' => 'Session expired',
                    'redirect' => base_url('auth/login')
                ])->setStatusCode(401);
            }
            
            return redirect()->to('auth/login')->with('error', 'Your session has expired. Please log in again.');
        }
    }

    /**
     * Allows After filters to inspect and modify the response
     * object as needed. This method does not allow any way
     * to stop execution of other after filters, short of
     * throwing an Exception or Error.
     *
     * @param RequestInterface  $request
     * @param ResponseInterface $response
     * @param array|null        $arguments
     *
     * @return ResponseInterface|void
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Log successful access for security monitoring
        if (auth()->loggedIn()) {
            $this->logUserActivity($request);
        }
    }

    /**
     * Update user's last activity timestamp
     */
    private function updateLastActivity(): void
    {
        session()->set('last_activity', time());
    }

    /**
     * Check if session has expired
     */
    private function isSessionExpired(): bool
    {
        $sessionTimeout = config('Auth')->sessionTimeout ?? 7200; // 2 hours default
        $lastActivity = session()->get('last_activity');
        
        if (!$lastActivity) {
            return false;
        }
        
        return (time() - $lastActivity) > $sessionTimeout;
    }

    /**
     * Log user activity for security monitoring
     */
    private function logUserActivity(RequestInterface $request): void
    {
        // Only log significant activities to avoid spam
        $method = $request->getMethod();
        $uri = $request->getUri()->getPath();

        // Skip logging for assets, AJAX polling, etc.
        if (strpos($uri, '/assets/') !== false ||
            strpos($uri, '/api/poll') !== false ||
            ($method === 'GET' && strpos($uri, '/admin/dashboard') !== false)) {
            return;
        }

        // Log to file or database as needed
        log_message('info', sprintf(
            'User Activity: User ID %d accessed %s %s from IP %s',
            auth()->id(),
            $method,
            $uri,
            $request->getIPAddress()
        ));
    }

    /**
     * Log security events to database
     */
    private function logSecurityEvent(?int $userId, string $event, array $data = [], string $severity = 'medium'): void
    {
        try {
            $db = \Config\Database::connect();
            $builder = $db->table('security_audit_log');

            $logData = [
                'user_id' => $userId,
                'event' => $event,
                'data' => json_encode($data),
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
                'severity' => $severity,
                'created_at' => date('Y-m-d H:i:s')
            ];

            $builder->insert($logData);
        } catch (\Exception $e) {
            // Fallback to file logging if database fails
            log_message('error', 'Security Event: ' . $event . ' - ' . json_encode($data));
        }
    }

    /**
     * Detect suspicious activity patterns
     */
    private function detectSuspiciousActivity(int $userId, string $ipAddress, string $userAgent): bool
    {
        try {
            $db = \Config\Database::connect();
            $builder = $db->table('security_audit_log');

            // Check for multiple failed attempts in last 15 minutes
            $recentFailures = $builder
                ->where('user_id', $userId)
                ->where('event', 'unauthorized_access_attempt')
                ->where('created_at >', date('Y-m-d H:i:s', strtotime('-15 minutes')))
                ->countAllResults();

            if ($recentFailures > 5) {
                return true;
            }

            // Check for IP address changes in short time
            $recentIPs = $builder
                ->select('ip_address')
                ->where('user_id', $userId)
                ->where('created_at >', date('Y-m-d H:i:s', strtotime('-1 hour')))
                ->groupBy('ip_address')
                ->countAllResults();

            if ($recentIPs > 3) {
                return true;
            }

            return false;
        } catch (\Exception $e) {
            // If detection fails, err on the side of caution
            return false;
        }
    }
}
