<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

/**
 * Enhanced Authentication Filter
 * Provides optimized authentication checking with session management
 */
class AuthFilter implements FilterInterface
{
    /**
     * Do whatever processing this filter needs to do.
     * By default it should not return anything during
     * normal execution. However, when an abnormal state
     * is found, it should return an instance of
     * CodeIgniter\HTTP\Response. If it does, script
     * execution will end and that Response will be
     * sent back to the client, allowing for error pages,
     * redirects, etc.
     *
     * @param RequestInterface $request
     * @param array|null       $arguments
     *
     * @return RequestInterface|ResponseInterface|string|void
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        // Check if user is authenticated
        if (!auth()->loggedIn()) {
            // Store the intended URL for redirect after login
            session()->set('redirect_url', current_url());
            
            // If AJAX request, return JSON response
            if ($request->isAJAX()) {
                return service('response')->setJSON([
                    'success' => false,
                    'message' => 'Authentication required',
                    'redirect' => base_url('auth/login')
                ])->setStatusCode(401);
            }
            
            // Regular request, redirect to login
            return redirect()->to('auth/login')->with('error', 'Please log in to access this page.');
        }

        // Check if user account is active
        $user = auth()->user();
        if (!$user->isActivated()) {
            auth()->logout();
            
            if ($request->isAJAX()) {
                return service('response')->setJSON([
                    'success' => false,
                    'message' => 'Account not activated',
                    'redirect' => base_url('auth/login')
                ])->setStatusCode(403);
            }
            
            return redirect()->to('auth/login')->with('error', 'Your account is not activated. Please check your email.');
        }

        // Update last activity timestamp for session management
        $this->updateLastActivity();
        
        // Check for session timeout (optional)
        if ($this->isSessionExpired()) {
            auth()->logout();
            
            if ($request->isAJAX()) {
                return service('response')->setJSON([
                    'success' => false,
                    'message' => 'Session expired',
                    'redirect' => base_url('auth/login')
                ])->setStatusCode(401);
            }
            
            return redirect()->to('auth/login')->with('error', 'Your session has expired. Please log in again.');
        }
    }

    /**
     * Allows After filters to inspect and modify the response
     * object as needed. This method does not allow any way
     * to stop execution of other after filters, short of
     * throwing an Exception or Error.
     *
     * @param RequestInterface  $request
     * @param ResponseInterface $response
     * @param array|null        $arguments
     *
     * @return ResponseInterface|void
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Log successful access for security monitoring
        if (auth()->loggedIn()) {
            $this->logUserActivity($request);
        }
    }

    /**
     * Update user's last activity timestamp
     */
    private function updateLastActivity(): void
    {
        session()->set('last_activity', time());
    }

    /**
     * Check if session has expired
     */
    private function isSessionExpired(): bool
    {
        $sessionTimeout = config('Auth')->sessionTimeout ?? 7200; // 2 hours default
        $lastActivity = session()->get('last_activity');
        
        if (!$lastActivity) {
            return false;
        }
        
        return (time() - $lastActivity) > $sessionTimeout;
    }

    /**
     * Log user activity for security monitoring
     */
    private function logUserActivity(RequestInterface $request): void
    {
        // Only log significant activities to avoid spam
        $method = $request->getMethod();
        $uri = $request->getUri()->getPath();
        
        // Skip logging for assets, AJAX polling, etc.
        if (strpos($uri, '/assets/') !== false || 
            strpos($uri, '/api/poll') !== false ||
            ($method === 'GET' && strpos($uri, '/admin/dashboard') !== false)) {
            return;
        }

        // Log to file or database as needed
        log_message('info', sprintf(
            'User Activity: User ID %d accessed %s %s from IP %s',
            auth()->id(),
            $method,
            $uri,
            $request->getIPAddress()
        ));
    }
}
