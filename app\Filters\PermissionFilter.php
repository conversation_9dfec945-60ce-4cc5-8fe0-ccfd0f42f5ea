<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

/**
 * Permission-based Authorization Filter
 * Checks if user has required permissions for specific actions
 */
class PermissionFilter implements FilterInterface
{
    /**
     * Do whatever processing this filter needs to do.
     *
     * @param RequestInterface $request
     * @param array|null       $arguments Expected format: ['permission1', 'permission2'] or ['permission1']
     *
     * @return RequestInterface|ResponseInterface|string|void
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        // Ensure user is authenticated first
        if (!auth()->loggedIn()) {
            return $this->unauthorizedResponse($request, 'Authentication required');
        }

        $user = auth()->user();
        
        // If no specific permissions required, just check if authenticated
        if (empty($arguments)) {
            return;
        }

        // Check if user has any of the required permissions
        $hasPermission = false;
        foreach ($arguments as $permission) {
            if ($user->can($permission)) {
                $hasPermission = true;
                break;
            }
        }

        if (!$hasPermission) {
            // Log unauthorized access attempt
            log_message('warning', sprintf(
                'Unauthorized access attempt: User ID %d tried to access %s %s without required permissions: %s',
                auth()->id(),
                $request->getMethod(),
                $request->getUri()->getPath(),
                implode(', ', $arguments)
            ));

            return $this->unauthorizedResponse($request, 'Insufficient permissions');
        }

        // Log successful permission check for audit
        log_message('info', sprintf(
            'Permission granted: User ID %d accessed %s %s with permissions: %s',
            auth()->id(),
            $request->getMethod(),
            $request->getUri()->getPath(),
            implode(', ', $arguments)
        ));
    }

    /**
     * Allows After filters to inspect and modify the response
     * object as needed.
     *
     * @param RequestInterface  $request
     * @param ResponseInterface $response
     * @param array|null        $arguments
     *
     * @return ResponseInterface|void
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Add security headers for authorized requests
        $response->setHeader('X-Content-Type-Options', 'nosniff');
        $response->setHeader('X-Frame-Options', 'DENY');
        $response->setHeader('X-XSS-Protection', '1; mode=block');
        
        return $response;
    }

    /**
     * Return appropriate unauthorized response
     */
    private function unauthorizedResponse(RequestInterface $request, string $message): ResponseInterface
    {
        if ($request->isAJAX()) {
            return service('response')->setJSON([
                'success' => false,
                'message' => $message,
                'code' => 'INSUFFICIENT_PERMISSIONS'
            ])->setStatusCode(403);
        }

        // For regular requests, redirect to access denied page or dashboard
        return redirect()->to('/admin')->with('error', 'You do not have permission to access this resource.');
    }
}
